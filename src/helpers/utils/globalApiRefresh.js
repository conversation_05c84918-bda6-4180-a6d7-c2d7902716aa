/**
 * Global API Refresh Utility
 * 
 * This utility provides a centralized way to refresh all APIs across the application
 * when certain events occur (like account switching, brand changes, etc.)
 */

import { accountSwitchEmitter } from '../context/MultiAccountContext';

/**
 * Manually trigger a global API refresh
 * This can be called from anywhere in the application to force refresh all APIs
 * 
 * @param {Object} eventData - Optional event data to pass to listeners
 */
export const triggerGlobalApiRefresh = (eventData = {}) => {
  console.log('🔄 Triggering global API refresh...', eventData);
  
  accountSwitchEmitter.emit({
    ...eventData,
    timestamp: Date.now(),
    action: 'MANUAL_REFRESH'
  });
};

/**
 * Subscribe to global refresh events
 * This is a wrapper around the account switch emitter for easier usage
 * 
 * @param {Function} callback - Function to call when refresh is triggered
 * @returns {Function} Unsubscribe function
 */
export const subscribeToGlobalRefresh = (callback) => {
  return accountSwitchEmitter.subscribe(callback);
};

/**
 * Common refresh patterns for different types of components
 */
export const RefreshPatterns = {
  /**
   * Standard API refresh pattern
   * Calls multiple API functions in parallel
   */
  apiRefresh: async (apiFunctions = []) => {
    try {
      console.log(`🔄 Refreshing ${apiFunctions.length} APIs...`);
      await Promise.all(
        apiFunctions.map(async (apiFunc) => {
          if (typeof apiFunc === 'function') {
            return await apiFunc();
          }
          return Promise.resolve();
        })
      );
      console.log('✅ API refresh completed');
    } catch (error) {
      console.error('❌ Error during API refresh:', error);
    }
  },

  /**
   * Redux action refresh pattern
   * Dispatches multiple Redux actions
   */
  reduxRefresh: (dispatch, actions = []) => {
    console.log(`🔄 Dispatching ${actions.length} Redux actions...`);
    actions.forEach(action => {
      if (typeof action === 'function') {
        dispatch(action());
      } else {
        dispatch(action);
      }
    });
    console.log('✅ Redux refresh completed');
  },

  /**
   * State reset pattern
   * Resets component state to initial values
   */
  stateReset: (setters = []) => {
    console.log(`🔄 Resetting ${setters.length} state values...`);
    setters.forEach(setter => {
      if (typeof setter === 'function') {
        setter();
      }
    });
    console.log('✅ State reset completed');
  },

  /**
   * Cache clear pattern
   * Clears various caches and stored data
   */
  cacheClear: (clearFunctions = []) => {
    console.log(`🔄 Clearing ${clearFunctions.length} caches...`);
    clearFunctions.forEach(clearFunc => {
      if (typeof clearFunc === 'function') {
        clearFunc();
      }
    });
    console.log('✅ Cache clear completed');
  }
};

/**
 * Comprehensive refresh function that handles all common patterns
 * 
 * @param {Object} options - Refresh options
 * @param {Array} options.apis - API functions to call
 * @param {Function} options.dispatch - Redux dispatch function
 * @param {Array} options.actions - Redux actions to dispatch
 * @param {Array} options.stateResets - State reset functions
 * @param {Array} options.cacheClear - Cache clear functions
 */
export const performComprehensiveRefresh = async (options = {}) => {
  const {
    apis = [],
    dispatch = null,
    actions = [],
    stateResets = [],
    cacheClear = []
  } = options;

  console.log('🔄 Starting comprehensive refresh...');

  try {
    // Clear caches first
    if (cacheClear.length > 0) {
      RefreshPatterns.cacheClear(cacheClear);
    }

    // Reset state
    if (stateResets.length > 0) {
      RefreshPatterns.stateReset(stateResets);
    }

    // Dispatch Redux actions
    if (dispatch && actions.length > 0) {
      RefreshPatterns.reduxRefresh(dispatch, actions);
    }

    // Call APIs last (after state is reset)
    if (apis.length > 0) {
      await RefreshPatterns.apiRefresh(apis);
    }

    console.log('✅ Comprehensive refresh completed successfully');
  } catch (error) {
    console.error('❌ Error during comprehensive refresh:', error);
    throw error;
  }
};

/**
 * Debug utility to log all active refresh listeners
 */
export const debugRefreshListeners = () => {
  console.log('🔍 Active refresh listeners:', accountSwitchEmitter.listeners.length);
  return accountSwitchEmitter.listeners.length;
};

export default {
  triggerGlobalApiRefresh,
  subscribeToGlobalRefresh,
  RefreshPatterns,
  performComprehensiveRefresh,
  debugRefreshListeners
};
