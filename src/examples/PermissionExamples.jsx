import React from 'react';
import { usePermissions } from '../helpers/hooks/usePermissions';
import PermissionGuard from '../components/common/PermissionGuard';

/**
 * Example component demonstrating how to use the permission system
 * This file shows various ways to implement permission-based restrictions
 */
const PermissionExamples = () => {
  const { 
    hasPermission, 
    hasAllPermissions, 
    hasAnyPermission,
    getUserPermissions,
    formattedPermissions,
    PERMISSIONS 
  } = usePermissions();

  return (
    <div className="permission-examples p-6">
      <h1 className="text-2xl font-bold mb-6">Permission System Examples</h1>

      {/* Display current permissions */}
      <div className="mb-8 p-4 bg-gray-100 rounded">
        <h2 className="text-lg font-semibold mb-2">Current User Permissions:</h2>
        <p>Active permissions: {getUserPermissions().join(', ') || 'None'}</p>
        <div className="mt-2 text-sm">
          <div>Post: {formattedPermissions.Post ? '✅' : '❌'}</div>
          <div>Message: {formattedPermissions.Message ? '✅' : '❌'}</div>
          <div>Analytics: {formattedPermissions.Analytics ? '✅' : '❌'}</div>
          <div>User Management: {formattedPermissions.User_Management ? '✅' : '❌'}</div>
          <div>Brand Management: {formattedPermissions.Brand_Management ? '✅' : '❌'}</div>
          <div>Block/Unblock: {formattedPermissions.Block_UnBlock ? '✅' : '❌'}</div>
          <div>Feedback: {formattedPermissions.FeedBack ? '✅' : '❌'}</div>
        </div>
      </div>

      {/* Example 1: Simple button with permission check */}
      <div className="mb-6">
        <h3 className="text-lg font-semibold mb-2">Example 1: Simple Permission Guard</h3>
        <PermissionGuard 
          permission={PERMISSIONS.POST}
          fallback={<p className="text-gray-500">You don't have permission to create posts</p>}
        >
          <button className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">
            Create Post
          </button>
        </PermissionGuard>
      </div>

      {/* Example 2: Multiple permissions (ALL required) */}
      <div className="mb-6">
        <h3 className="text-lg font-semibold mb-2">Example 2: Multiple Permissions (ALL required)</h3>
        <PermissionGuard 
          permission={[PERMISSIONS.USER_MANAGEMENT, PERMISSIONS.BRAND_MANAGEMENT]}
          mode="all"
          fallback={<p className="text-gray-500">You need both User Management and Brand Management permissions</p>}
        >
          <button className="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600">
            Admin Panel
          </button>
        </PermissionGuard>
      </div>

      {/* Example 3: Multiple permissions (ANY required) */}
      <div className="mb-6">
        <h3 className="text-lg font-semibold mb-2">Example 3: Multiple Permissions (ANY required)</h3>
        <PermissionGuard 
          permission={[PERMISSIONS.ANALYTICS, PERMISSIONS.USER_MANAGEMENT]}
          mode="any"
          fallback={<p className="text-gray-500">You need either Analytics or User Management permission</p>}
        >
          <button className="bg-purple-500 text-white px-4 py-2 rounded hover:bg-purple-600">
            View Reports
          </button>
        </PermissionGuard>
      </div>

      {/* Example 4: Disabled button instead of hiding */}
      <div className="mb-6">
        <h3 className="text-lg font-semibold mb-2">Example 4: Disabled Button</h3>
        <PermissionGuard 
          permission={PERMISSIONS.MESSAGE}
          disabled={!hasPermission(PERMISSIONS.MESSAGE)}
          disabledClassName="opacity-50 cursor-not-allowed"
          disabledProps={{ title: "You don't have permission to send messages" }}
        >
          <button className="bg-orange-500 text-white px-4 py-2 rounded hover:bg-orange-600">
            Send Message
          </button>
        </PermissionGuard>
      </div>

      {/* Example 5: Using hook directly in component logic */}
      <div className="mb-6">
        <h3 className="text-lg font-semibold mb-2">Example 5: Using Hook Directly</h3>
        <div className="space-y-2">
          {hasPermission(PERMISSIONS.POST) && (
            <button className="bg-blue-500 text-white px-4 py-2 rounded mr-2">
              Create Post
            </button>
          )}
          
          {hasPermission(PERMISSIONS.ANALYTICS) && (
            <button className="bg-indigo-500 text-white px-4 py-2 rounded mr-2">
              View Analytics
            </button>
          )}
          
          {hasAnyPermission([PERMISSIONS.USER_MANAGEMENT, PERMISSIONS.BRAND_MANAGEMENT]) && (
            <button className="bg-red-500 text-white px-4 py-2 rounded mr-2">
              Management Tools
            </button>
          )}
        </div>
      </div>

      {/* Example 6: Conditional rendering with complex logic */}
      <div className="mb-6">
        <h3 className="text-lg font-semibold mb-2">Example 6: Complex Conditional Logic</h3>
        <div className="p-4 border rounded">
          {(() => {
            if (hasAllPermissions([PERMISSIONS.USER_MANAGEMENT, PERMISSIONS.BRAND_MANAGEMENT])) {
              return <p className="text-green-600">Full admin access granted</p>;
            } else if (hasAnyPermission([PERMISSIONS.USER_MANAGEMENT, PERMISSIONS.BRAND_MANAGEMENT])) {
              return <p className="text-yellow-600">Partial admin access granted</p>;
            } else if (hasPermission(PERMISSIONS.POST)) {
              return <p className="text-blue-600">Content creator access granted</p>;
            } else {
              return <p className="text-red-600">Limited access - contact admin for more permissions</p>;
            }
          })()}
        </div>
      </div>

      {/* Example 7: Navigation menu with permission-based items */}
      <div className="mb-6">
        <h3 className="text-lg font-semibold mb-2">Example 7: Navigation Menu</h3>
        <nav className="bg-gray-800 text-white p-4 rounded">
          <ul className="flex space-x-4">
            <li><a href="#" className="hover:text-gray-300">Dashboard</a></li>
            
            <PermissionGuard permission={PERMISSIONS.POST}>
              <li><a href="#" className="hover:text-gray-300">Posts</a></li>
            </PermissionGuard>
            
            <PermissionGuard permission={PERMISSIONS.MESSAGE}>
              <li><a href="#" className="hover:text-gray-300">Messages</a></li>
            </PermissionGuard>
            
            <PermissionGuard permission={PERMISSIONS.ANALYTICS}>
              <li><a href="#" className="hover:text-gray-300">Analytics</a></li>
            </PermissionGuard>
            
            <PermissionGuard permission={[PERMISSIONS.USER_MANAGEMENT, PERMISSIONS.BRAND_MANAGEMENT]} mode="any">
              <li><a href="#" className="hover:text-gray-300">Admin</a></li>
            </PermissionGuard>
          </ul>
        </nav>
      </div>

      {/* Example 8: Form fields with permission checks */}
      <div className="mb-6">
        <h3 className="text-lg font-semibold mb-2">Example 8: Form with Permission Checks</h3>
        <form className="space-y-4 p-4 border rounded">
          <div>
            <label className="block text-sm font-medium mb-1">Title</label>
            <input type="text" className="w-full p-2 border rounded" />
          </div>
          
          <PermissionGuard permission={PERMISSIONS.POST}>
            <div>
              <label className="block text-sm font-medium mb-1">Content</label>
              <textarea className="w-full p-2 border rounded" rows="3"></textarea>
            </div>
          </PermissionGuard>
          
          <PermissionGuard permission={PERMISSIONS.BRAND_MANAGEMENT}>
            <div>
              <label className="block text-sm font-medium mb-1">Brand Settings</label>
              <select className="w-full p-2 border rounded">
                <option>Brand A</option>
                <option>Brand B</option>
              </select>
            </div>
          </PermissionGuard>
          
          <div className="flex space-x-2">
            <button type="submit" className="bg-blue-500 text-white px-4 py-2 rounded">
              Save
            </button>
            
            <PermissionGuard permission={PERMISSIONS.POST}>
              <button type="button" className="bg-green-500 text-white px-4 py-2 rounded">
                Publish
              </button>
            </PermissionGuard>
          </div>
        </form>
      </div>
    </div>
  );
};

export default PermissionExamples;
