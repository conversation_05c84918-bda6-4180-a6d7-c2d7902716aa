import React from "react";
import { Navigate } from "react-router-dom";
import Sidebar from "./layouts/sidebar";
import AuthLayout from "./layouts/auth_layout/index.jsx";
import SidebarLayout from "./layouts/privacy_layout/index.jsx";
// USER
const Home = React.lazy(() => import("./views/pages/dashboard/home"));
const PostFeed = React.lazy(() => import("./views/pages/feed"));
const Subscription = React.lazy(() => import("./views/common/subscription"));
const UserManage = React.lazy(() =>
  import("./views/pages/usermanagement/usermanagement")
);

const ScheduledPost = React.lazy(() =>
  import("./views/pages/scheduledpost/scheduledpost")
);
const One = React.lazy(() => import("./views/components/chat/Index"));
const Analytics = React.lazy(() =>
  import("./views/components/analytics/index")
);
const Dashboard = React.lazy(() =>
  import("./views/admin/pages/dashboard/index")
);

const PrivacyPolicy = React.lazy(() =>
  import("./views/pages/Privacy-Terms/privacy-policy")
);

const TermsCondition = React.lazy(() => import("./views/pages/Term/terms"));

const PostDetails = React.lazy(() => import("./views/admin/pages/postDetails"));
const ContactUs = React.lazy(() => import("./views/pages/contactus/contactus"));

const UserManagement = React.lazy(() =>
  import("./views/admin/pages/user-management")
);
const Profile = React.lazy(() => import("./views/pages/profile/profile"));
const SinglePost = React.lazy(() =>
  import("./views/components/profile/SiglePost")
);
const ShareProfile = React.lazy(() =>
  import("./views/pages/shareprofile/shareprofile")
);
const SignIn = React.lazy(() => import("./views/pages/auth/sign-in"));
const SignUp = React.lazy(() => import("./views/pages/auth/sign-up"));
const Forgot = React.lazy(() => import("./views/pages/auth/forgot-password"));
const Reset = React.lazy(() => import("./views/pages/auth/reset-password"));
const Feedback = React.lazy(() =>
  import("./views/components/feedback/index.js")
);
const Planner = React.lazy(() => import("./views/components/Planner/index.js"));
const Brands = React.lazy(() => import("./views/pages/brands/brands"));
const Live = React.lazy(() => import("./views/pages/live_stream/live"));

const RoleManagement = React.lazy(() =>
  import("./views/pages/Role-Mangement/role-manage")
);

const TypeSelection = React.lazy(() =>
  import("./views/pages/auth/type-selection")
);
const OtpVerification = React.lazy(() =>
  import("./views/pages/auth/otp-verify")
);
const DataNotFound = React.lazy(() =>
  import("./views/components/custom/DataNotFound")
);
const InstaLogin = React.lazy(() =>
  import("./views/pages/auth/instagram/login")
);
const InstaResponse = React.lazy(() =>
  import("./views/pages/auth/instagram/response")
);
const NotFoundPage = React.lazy(() => import("./views/pages/exception"));
const BadGatewayPage = React.lazy(() =>
  import("./views/pages/exception/BadGatewayPage")
);

const instaAuthRoutes = {
  path: "/oauth/instagram",
  children: [
    { path: "/oauth/instagram", element: <InstaLogin /> },
    { path: "/oauth/instagram/response", element: <InstaResponse /> },
  ],
};

const publicOther = [
  {
    path: "*",
    element: <NotFoundPage />,
  },
  {
    path: "/contactus",
    element: <ContactUs />,
  },
  {
    path: "/",
    element: <SidebarLayout />,
    children: [
      { path: "/privacy-policy", element: <PrivacyPolicy /> },
      { path: "/terms-of-use", element: <TermsCondition /> },
      { path: "/contact-us", element: <ContactUs /> },
    ],
  },
];

const userRoutes = [
  {
    path: "/",
    element: <Sidebar />,
    children: [
      { path: "/", element: <Navigate to="/dashboard" /> },
      { path: "/dashboard", element: <Home /> },
      { path: "/scheduled-post", element: <ScheduledPost /> },
      { path: "/chat", element: <One /> },
      { path: "/analytics", element: <Analytics /> },
      { path: "/feed", element: <PostFeed /> },
      { path: "/profile", element: <Profile /> },
      { path: "/post/:id", element: <SinglePost /> },
      { path: "/subscription", element: <Subscription /> },
      { path: "/UserManagement", element: <UserManage /> },
      { path: "/feedback", element: <Feedback /> },
      { path: "/planner", element: <Planner /> },
      { path: "/brands", element: <Brands /> },
      { path: "/live", element: <Live /> },

      { path: "/role-manage", element: <RoleManagement /> },

      { path: "/view-profile/", element: <ShareProfile /> },
      { path: "/404", element: <DataNotFound /> },
      { path: "/bad-gateway", element: <BadGatewayPage /> },
    ],
  },
  {
    path: "/view-profile/",
    element: <ShareProfile />,
  },
  instaAuthRoutes,
  ...publicOther,
];

const adminRoutes = [
  {
    path: "/admin",
    element: <Sidebar />,
    children: [
      { path: "/admin", element: <Navigate to="/admin/user-management" /> },
      // { path: "/admin/dashboard", element: <Dashboard /> },
      // { path: "/admin/post-details", element: <PostDetails /> },
      { path: "/admin/user-management", element: <UserManagement /> },
      { path: "/admin/404", element: <DataNotFound /> },
    ],
  },
  {
    path: "/view-profile/",
    element: <ShareProfile />,
  },
  instaAuthRoutes,
  ...publicOther,
];

const publicRoute = [
  {
    path: "/",
    element: <AuthLayout />,
    children: [
      { path: "/", element: <Navigate to="/sign-in" /> },
      { path: "/sign-in", element: <SignIn /> },
      { path: "/type-selection", element: <TypeSelection /> },
      { path: "/forgot-password", element: <Forgot /> },
      { path: "/sign-up", element: <SignUp /> },
      { path: "/otp-verify", element: <OtpVerification /> },
      { path: "/reset-password", element: <Reset /> },
      { path: "/bad-gateway", element: <BadGatewayPage /> },
    ],
  },
  {
    path: "/bad-gateway",
    element: <BadGatewayPage />,
  },
  {
    path: "/view-profile/",
    element: <ShareProfile />,
  },
  instaAuthRoutes,
  ...publicOther,
];

export { publicRoute, userRoutes, adminRoutes };
