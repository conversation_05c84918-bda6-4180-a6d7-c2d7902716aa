# Account Switch API Refresh System

## Overview

This system ensures that when a user switches accounts from the navbar dropdown, **ALL APIs across the entire application are automatically refreshed** with the new user's data. No more partial updates or stale data!

## How It Works

### 1. Global Event System
- When a user switches accounts, a global event is emitted
- All components that need to refresh their data listen to this event
- APIs are automatically called with the new user's token and data

### 2. Custom Hooks
Three custom hooks are provided to make integration easy:

#### `useAccountSwitchRefresh`
Basic hook for any refresh callback:
```javascript
import { useAccountSwitchRefresh } from '../../../helpers/hooks/useAccountSwitchRefresh.js';

const MyComponent = () => {
  const refreshData = () => {
    // Your refresh logic here
    fetchUserData();
    loadDashboard();
  };

  useAccountSwitchRefresh(refreshData);
  
  return <div>My Component</div>;
};
```

#### `useAccountSwitchApiRefresh`
Hook specifically for API calls:
```javascript
import { useAccountSwitchApiRefresh } from '../../../helpers/hooks/useAccountSwitchRefresh.js';

const MyComponent = () => {
  const fetchUserProfile = async () => { /* API call */ };
  const fetchUserPosts = async () => { /* API call */ };
  const fetchDashboard = async () => { /* API call */ };

  // All these APIs will be called when account switches
  useAccountSwitchApiRefresh([
    fetchUserProfile,
    fetchUserPosts,
    fetchDashboard
  ], [fetchUserProfile, fetchUserPosts, fetchDashboard]);
  
  return <div>My Component</div>;
};
```

#### `useAccountSwitchReduxRefresh`
Hook for Redux actions:
```javascript
import { useAccountSwitchReduxRefresh } from '../../../helpers/hooks/useAccountSwitchRefresh.js';
import { fetchProfile, fetchPosts } from '../../../redux/slices/profileSlice';

const MyComponent = () => {
  const dispatch = useDispatch();

  // These Redux actions will be dispatched when account switches
  useAccountSwitchReduxRefresh(dispatch, [
    fetchProfile,
    fetchPosts
  ], [dispatch]);
  
  return <div>My Component</div>;
};
```

## Components Already Updated

The following components have been updated to automatically refresh on account switch:

### ✅ Dashboard (`src/views/components/dashboard/index.jsx`)
- `fetchUserProfile`
- `fetchUserHomeData`
- `fetchDashboardData`
- `getUserStatus`

### ✅ Profile (`src/views/components/profile/index.jsx`)
- Redux `fetchProfile` action

### ✅ User Management (`src/views/components/user_management/index.jsx`)
- `GetRoleData`
- `GetPendingInvitedUsers`
- `GetPendingInviteeUsers`
- `GetInviteeUsers`
- `GetInvitedUsers`
- `fetchUsers`

### ✅ Chat (`src/views/components/chat/Index.jsx`)
- `fetchUsers`
- `fetchFlowkarUsers`
- Message state reset and refresh

## Adding to New Components

To add account switch refresh to a new component:

### Step 1: Import the hook
```javascript
import { useAccountSwitchApiRefresh } from '../../../helpers/hooks/useAccountSwitchRefresh.js';
```

### Step 2: Add the hook call
```javascript
const MyComponent = () => {
  const fetchMyData = async () => { /* Your API call */ };
  const loadMyContent = async () => { /* Another API call */ };

  // Add this hook to refresh APIs on account switch
  useAccountSwitchApiRefresh([
    fetchMyData,
    loadMyContent
  ], [fetchMyData, loadMyContent]);

  // Rest of your component...
};
```

## Manual Refresh

You can also manually trigger a global refresh:

```javascript
import { triggerGlobalApiRefresh } from '../../../helpers/utils/globalApiRefresh.js';

// Trigger refresh manually
triggerGlobalApiRefresh({
  reason: 'Manual refresh requested',
  source: 'Settings page'
});
```

## Testing

To test if the system is working:

1. **Switch accounts** from the navbar dropdown
2. **Check browser console** - you should see logs like:
   ```
   Account switch detected, refreshing data...
   🔄 Refreshing 4 APIs...
   ✅ API refresh completed
   ```
3. **Verify data updates** - all components should show the new user's data

## Debugging

To see how many components are listening for account switches:

```javascript
import { debugRefreshListeners } from '../../../helpers/utils/globalApiRefresh.js';

console.log('Active listeners:', debugRefreshListeners());
```

## Benefits

✅ **Complete Data Refresh**: All APIs are refreshed, not just some  
✅ **Automatic**: No manual intervention needed  
✅ **Consistent**: Same pattern across all components  
✅ **Reliable**: Handles errors gracefully  
✅ **Performant**: APIs are called in parallel  
✅ **Easy to Use**: Simple hooks for integration  

## Migration Guide

For existing components that need to be updated:

1. **Identify API calls** that need refreshing
2. **Import the appropriate hook**
3. **Add the hook call** with your API functions
4. **Test** by switching accounts

The system is backward compatible - components without the hooks will continue to work, they just won't auto-refresh on account switch.
