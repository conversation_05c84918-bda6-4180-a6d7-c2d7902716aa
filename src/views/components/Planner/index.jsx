import React, {
  useState,
  useMemo,
  useCallback,
  useEffect,
  useRef,
} from "react";
import {
  Calendar,
  ChevronLeft,
  ChevronRight,
  ChevronDown,
  ChevronUp,
} from "lucide-react";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  Box,
  Grid,
  Chip,
  IconButton,
  Avatar,
  TextField,
} from "@mui/material";
import { Close as CloseIcon } from "@mui/icons-material";
import plus_icon from "../../../assets/images/Upload_Post/plus.svg";
import { useBrand } from "../../../helpers/context/BrandContext";
import apiInstance from "../../../helpers/Axios/axiosINstance";
import { URL } from "../../../helpers/constant/Url";
import siteConstant from "../../../helpers/constant/siteConstant";
import Pagination from "../../../views/admin/common/paginationCommon";
import Delete from "../../../assets/images/svg_icon/delete.svg";
import editIcon from "../../../assets/images/svg_icon/editIcon.svg";
import DeleteScheduledPostModel from "./deletePopUp";
import { getFileType } from "../../../helpers/constant/utils";
import { setApiMessage } from "../../../helpers/context/toaster";
import Spinner from "../../../helpers/UI/Spinner";
import Loader from "../../../helpers/UI/Loader";

const EditPostPopup = ({ post, onClose, onSave }) => {
  const [title, setTitle] = useState("");
  const [description, setDescription] = useState("");
  const [taggedIn, setTaggedIn] = useState("");
  const [hashtags, setHashtags] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const { selectedBrand } = useBrand ? useBrand() : { selectedBrand: null };

  useEffect(() => {
    if (post) {
      setTitle(post.title || "");
      setDescription(post.description || "");
      setTaggedIn(post.tagged_in || "");
      setHashtags(post.hashtags || []);
    }
  }, [post]);

  if (!post) return null;

  const handleSubmit = async () => {
    try {
      setIsLoading(true);
      const formData = new FormData();
      formData.append("post_id", post.id);
      formData.append("title", title);
      formData.append("tagged_in", taggedIn);
      formData.append("description", description);
      formData.append(
        "hashtags",
        JSON.stringify(hashtags.length > 0 ? hashtags : [])
      );

      const response = await apiInstance.post("update-post/", formData, {
        headers: {
          brand: selectedBrand?.id,
        },
      });
      if (response.status === 200)
        setApiMessage("success", response?.data?.message);
      if (onClose) onClose();
      if (onSave) onSave();
    } catch (error) {
      console.error("Error updating post:", error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog
      open={true}
      onClose={onClose}
      maxWidth="md"
      fullWidth
      className="bg-transparent font-Ubuntu"
      PaperProps={{
        style: {
          maxHeight: "90vh",
          margin: "8px",
          borderRadius: "20px",
          boxShadow: "0 20px 60px rgba(0, 0, 0, 0.1)",
          border: "none",
        },
      }}
    >
      <div className="bg-white rounded-[20px] overflow-y-auto">
        <DialogTitle sx={{ padding: 0 }}>
          <Box
            sx={{
              padding: { xs: 2, sm: 3 },
              borderBottom: "1px solid #f1f5f9",
              backgroundColor: "#ffffff",
            }}
          >
            <Box
              display="flex"
              justifyContent="space-between"
              alignItems="center"
            >
              <Typography
                variant="h6"
                sx={{
                  fontWeight: "400",
                  color: "#000000",
                  fontSize: { xs: "1.125rem", sm: "1.25rem" },
                }}
              >
                Edit Post
              </Typography>
              <IconButton
                onClick={onClose}
                sx={{
                  color: "#64748b",
                  backgroundColor: "#f8fafc",
                  width: 40,
                  height: 40,
                  "&:hover": {
                    backgroundColor: "#f1f5f9",
                    color: "#475569",
                  },
                  transition: "all 0.2s ease",
                }}
              >
                <CloseIcon fontSize="small" />
              </IconButton>
            </Box>
          </Box>
        </DialogTitle>

        <DialogContent
          dividers={false}
          sx={{
            padding: { xs: 2, sm: 3, md: 4 },
            backgroundColor: "#ffffff",
          }}
        >
          {/* Post Preview - Non-editable section */}
          <Box
            mb={4}
            mt={4}
            display="flex"
            justifyContent="center"
            width="100%"
            px={{ xs: 1, sm: 2 }}
          >
            {post.files && post.files.length > 0 ? (
              <Grid
                container
                spacing={{ xs: 1, sm: 2 }}
                justifyContent="center"
                style={{
                  maxWidth: "100%",
                  width: "100%",
                }}
              >
                {post.files.map((file, index) => {
                  const fileUrl =
                    typeof file === "string"
                      ? file
                      : file.url || file.path || "";
                  const isImage = /\.(jpg|jpeg|png|gif|webp)$/i.test(fileUrl);
                  const isVideo = /\.(mp4|mov|avi|webm|MOV)$/i.test(fileUrl);
                  return (
                    <Grid
                      item
                      xs={12}
                      sm={post.files.length > 1 ? 6 : 12}
                      key={index}
                      style={{
                        display: "flex",
                        justifyContent: "center",
                        alignItems: "center",
                      }}
                    >
                      {isImage ? (
                        <img
                          src={fileUrl}
                          alt={`Post media ${index}`}
                          className="md:w-[340px] w-[250px] md:h-[360px] h-[250px] inline-block rounded-[16px] object-cover mx-auto"
                          style={{
                            boxShadow: "0 8px 25px rgba(0, 0, 0, 0.08)",
                            border: "1px solid #f1f5f9",
                          }}
                          onError={(e) => {
                            e.target.style.display = "none";
                          }}
                        />
                      ) : isVideo ? (
                        <video
                          controls
                          playsInline
                          preload="metadata"
                          className="md:w-[340px] w-[250px] md:h-[360px] h-[250px] inline-block rounded-[16px] object-cover mx-auto relative z-10"
                          style={{
                            boxShadow: "0 8px 25px rgba(0, 0, 0, 0.08)",
                            border: "1px solid #f1f5f9",
                            backgroundColor: "transparent",
                          }}
                        >
                          <source src={fileUrl} type="video/mp4" />
                          Your browser does not support the video tag.
                        </video>
                      ) : null}
                    </Grid>
                  );
                })}
              </Grid>
            ) : (
              <Box
                sx={{
                  backgroundColor: "#f8fafc",
                  borderRadius: "16px",
                  padding: { xs: 4, sm: 6, md: 8 },
                  textAlign: "center",
                  color: "#64748b",
                  width: "100%",
                  maxWidth: "400px",
                  border: "1px solid #e2e8f0",
                }}
              >
                <Typography variant="body1" fontWeight="500">
                  No media attached
                </Typography>
              </Box>
            )}
          </Box>

          {/* Editable Post Details Section */}
          <Box
            sx={{
              backgroundColor: "#ffffff",
              borderRadius: "16px",
              border: "1px solid #f1f5f9",
              overflow: "hidden",
            }}
          >
            <Grid container>
              {/* Title - Editable */}
              <Grid item xs={12}>
                <Box
                  sx={{
                    padding: { xs: 3, sm: 4 },
                    borderBottom: { xs: "1px solid #f1f5f9", md: "none" },
                    borderRight: { md: "1px solid #f1f5f9" },
                    height: "100%",
                  }}
                >
                  <Typography
                    variant="subtitle2"
                    sx={{
                      color: "#64748b",
                      fontWeight: "500",
                      fontSize: "0.875rem",
                      mb: 1,
                      letterSpacing: "0.5px",
                    }}
                  >
                    Title
                  </Typography>
                  <TextField
                    fullWidth
                    multiline
                    variant="outlined"
                    value={title}
                    onChange={(e) => setTitle(e.target.value)}
                    placeholder="Enter title"
                    sx={{
                      "& .MuiOutlinedInput-root": {
                        "& fieldset": {
                          borderColor: "#e0e0e0", // subtle static border
                          borderWidth: "1px",
                          borderRadius: "8px",
                        },
                        "&:hover fieldset": {
                          borderColor: "#e0e0e0", // prevent hover color change
                        },
                        "&.Mui-focused fieldset": {
                          borderColor: "#e0e0e0", // prevent focus color change
                        },
                      },
                      "& .MuiInputBase-input": {
                        color: "#563D39",
                        lineHeight: 1.6,
                        fontSize: "0.95rem",
                      },
                    }}
                  />
                </Box>
              </Grid>

              {/* Status - Non-editable */}
              {/* <Grid item xs={12} md={6}>
                <Box
                  sx={{
                    padding: { xs: 3, sm: 4 },
                    borderBottom: { xs: "1px solid #f1f5f9", md: "none" },
                    height: "100%",
                  }}
                >
                  <Typography
                    variant="subtitle2"
                    sx={{
                      color: "#64748b",
                      fontWeight: "500",
                      fontSize: "0.875rem",
                      mb: 1,
                      letterSpacing: "0.5px",
                    }}
                  >
                    Status
                  </Typography>
                  <Chip
                    label={post.status || "Unknown"}
                    sx={{
                      "& .MuiChip-label": {
                        color:
                          post.status === "scheduled" ? "#563D39" : "#166534",
                        fontWeight: 500,
                        fontSize: "0.875rem",
                      },
                      borderRadius: "8px",
                      height: "32px",
                    }}
                    size="small"
                  />
                </Box>
              </Grid> */}

              {/* Description - Editable */}
              <Grid item xs={12}>
                <Box
                  sx={{
                    padding: { xs: 3, sm: 4 },
                    borderTop: "1px solid #f1f5f9",
                  }}
                >
                  <Typography
                    variant="subtitle2"
                    sx={{
                      color: "#64748b",
                      fontWeight: "500",
                      fontSize: "0.875rem",
                      mb: 2,
                      letterSpacing: "0.5px",
                    }}
                  >
                    Description
                  </Typography>
                  <TextField
                    fullWidth
                    multiline
                    rows={4}
                    variant="outlined"
                    value={description}
                    onChange={(e) => setDescription(e.target.value)}
                    sx={{
                      "& .MuiOutlinedInput-root": {
                        "& fieldset": {
                          borderColor: "#e0e0e0", // subtle static border
                          borderWidth: "1px",
                          borderRadius: "8px",
                        },
                        "&:hover fieldset": {
                          borderColor: "#e0e0e0", // prevent hover color change
                        },
                        "&.Mui-focused fieldset": {
                          borderColor: "#e0e0e0", // prevent focus color change
                        },
                      },
                      "& .MuiInputBase-input": {
                        color: "#563D39",
                        lineHeight: 1.6,
                        fontSize: "0.95rem",
                      },
                    }}
                    placeholder="Enter description"
                  />
                </Box>
              </Grid>
            </Grid>
          </Box>
        </DialogContent>

        <DialogActions
          sx={{ padding: { xs: 2, sm: 3 }, justifyContent: "flex-end  " }}
        >
          <Button
            onClick={handleSubmit}
            variant="contained"
            disabled={isLoading}
            sx={{
              backgroundColor: "#563D39",
              borderRadius: "8px",
              color: "white",
              "&:hover": {
                backgroundColor: "#4a342f",
              },
              "&:disabled": {
                backgroundColor: "#e2e8f0",
                color: "#94a3b8",
              },
            }}
          >
            {isLoading ? "Saving..." : "Save"}
          </Button>
        </DialogActions>
      </div>
    </Dialog>
  );
};

const UploadPost = React.lazy(() => import("../UploadPost/index.jsx"));

// Constants
const MONTHS = [
  "January",
  "February",
  "March",
  "April",
  "May",
  "June",
  "July",
  "August",
  "September",
  "October",
  "November",
  "December",
];

const DAY_NAMES_SHORT = ["SUN", "MON", "TUE", "WED", "THU", "FRI", "SAT"];
const DAY_NAMES_LONG = [
  "Sunday",
  "Monday",
  "Tuesday",
  "Wednesday",
  "Thursday",
  "Friday",
  "Saturday",
];

const CalendarComponent = () => {
  const [currentDate, setCurrentDate] = useState(new Date());
  const [isUploadDialogOpen, setIsUploadDialogOpen] = useState(false);
  const [viewMode, setViewMode] = useState("Month");
  const [selectedDateRange] = useState(getDefaultDateRange());
  const [thirdPartyConnectedPlatfroms, setThirdPartyConnectedPlatfroms] =
    useState([]);
  const [isOpen, setIsOpen] = useState(false);
  const [selectedPlatform, setSelectedPlatform] = useState("all");
  const [plannerData, setPlannerData] = useState([]);
  const [selectedPost, setSelectedPost] = useState(null);
  const [listViewData, setListViewData] = useState([]);
  const [editPost, setEditPost] = useState(null);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);

  const [isListViewLoading, setIsListViewLoading] = useState(false);

  const [isLoadingPlatforms, setIsLoadingPlatforms] = useState(false);
  const [isEditPostPopupOpen, setIsEditPostPopupOpen] = useState(false);
  const [loading, setLoading] = useState(true);
  const [showFullTitle, setShowFullTitle] = useState(false);
  const [showFullDescription, setShowFullDescription] = useState(false);
  const [hasPostPermission, setHasPostPermission] = useState(false);
  const permissions = localStorage.getItem("userPermissions");
  console.log("Permission========>", permissions);

  const { selectedBrand, handleBrandSelect } = useBrand();

  const dropdownRef = useRef(null);

  const checkPermission = () => {
    const permissions = localStorage.getItem("userPermissions");
    if (permissions.includes(1)) {
      setHasPostPermission(true);
    }
  };

  useEffect(() => {
    checkPermission();
    console.log("hasPostPermission============>",hasPostPermission);
    
  }, []);

  const connectedThirdPartyPlatforms = async () => {
    try {
      setIsLoadingPlatforms(true);
      const response = await apiInstance.get(URL.GET_THIRDPARTY, {
        headers: {
          brand: selectedBrand?.id,
        },
      });
      const data = response?.data?.data;
      setThirdPartyConnectedPlatfroms(data);
    } catch (error) {
      console.log(error);
    } finally {
      setIsLoadingPlatforms(false);
    }
  };

  const handleEditClose = () => {
    setEditPost(null);
    setIsEditDialogOpen(false);
    setIsEditPostPopupOpen(false);
  };
  const handlePostClick = (post) => {
    setSelectedPost(post);
  };

  const handleClosePopup = () => {
    setSelectedPost(null);
  };

  useEffect(() => {
    if (selectedBrand?.id) {
      connectedThirdPartyPlatforms();
    }
  }, [selectedBrand]);

  const availablePlatforms = Object.entries(thirdPartyConnectedPlatfroms)
    .filter(([key, value]) => key !== "is_any_auth" && value === true)
    .map(([platform]) => platform);

  const getPlatformIcon = (platformName) => {
    if (typeof platformName !== "string") return null;

    const normalizedName = platformName.toLowerCase();

    const platformMapping = {
      instagram: "INSTAGRAM_ICON",
      facebook: "FACEBOOK_ICON",
      youtube: "YOUTUBE_ICON",
      pinterest: "PINTEREST_ICON",
      linkedin: "LINKEDIN_ICON",
      twitter: "TWITTER_ICON",
      x: "TWITTER_ICON",
      vimeo: "VIMEO_ICON",
      tumblr: "TUMBLR_ICON",
      reddit: "REDDIT_ICON",
      tiktok: "TIKTOK_ICON",
      threads: "THREADS_ICON",
      dailymotion: "DAILYMOTION_ICON",
      telegram: "TELEGRAM_ICON",
      mastodon: "MASTODON_ICON",
      flowkar: "FLOWKAR_F",
    };

    const iconKey = platformMapping[normalizedName];
    return iconKey ? siteConstant.SOCIAL_ICONS[iconKey] : null;
  };

  const handlePlatformSelect = (platform) => {
    setSelectedPlatform(platform);
    setIsOpen(false);
  };

  const toggleDropdown = () => {
    setIsOpen(!isOpen);
  };

  // Close dropdown when clicking outside
  useEffect(() => {
    if (!isOpen) return;
    function handleClickOutside(event) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    }
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isOpen]);

  const scheduledPostData = async () => {
    try {
      const params =
        selectedPlatform && selectedPlatform !== "all"
          ? { platform: selectedPlatform }
          : {};

      const response = await apiInstance.get(URL.SCHEDULED_POST_WEB, {
        params: params,
      });
      const data = response.data?.data;
      setPlannerData(data);
    } catch (error) {
      console.log(error);
    }
  };

  const handlePostSave = () => {
    fetchListViewData();
    setEditPost(null);
    setIsEditDialogOpen(false);
    setIsEditPostPopupOpen(false);
  };

  const fetchListViewData = async () => {
    try {
      setIsListViewLoading(true);
      const params =
        selectedPlatform && selectedPlatform !== "all"
          ? { platform: selectedPlatform.toLocaleLowerCase() }
          : {};

      // Replace with your new API endpoint for List View
      const response = await apiInstance.get(URL.WEB_LIST, {
        params: params,
        headers: {
          brand: selectedBrand?.id,
        },
      });

      const data = response?.data?.results?.data;
      setListViewData(data);
    } catch (error) {
      console.error("Error fetching list view data:", error);
    } finally {
      setIsListViewLoading(false);
    }
  };

  useEffect(() => {
    if (viewMode === "List") {
      fetchListViewData();
    } else {
      scheduledPostData(); // Original API call for other views
    }
  }, [selectedPlatform, isUploadDialogOpen, viewMode, selectedBrand]);

  // The useEffect remains the same
  useEffect(() => {
    scheduledPostData();
  }, [selectedPlatform, isUploadDialogOpen]);

  // Transform API data to events format
  const events = useMemo(() => {
    if (!plannerData || !Array.isArray(plannerData)) {
      return [];
    }

    return plannerData.map((post) => {
      const scheduledDate = new Date(post.scheduled_at);

      const time = scheduledDate.toLocaleTimeString("en-US", {
        hour12: false,
        hour: "2-digit",
        minute: "2-digit",
      });

      return {
        id: post.id,
        title:
          post.title && post.title.trim() !== "" && post.title.trim() !== "''"
            ? post.title
            : post.description || "Untitled Post",
        date: scheduledDate,
        time: time,
        platform: post.platform || "general",
        description: post.description,
        files: post.files,
        user: post.user,
        is_scheduled: post.is_scheduled,
        created_at: post.created_at,
        is_liked: post.is_liked,
      };
    });
  }, [plannerData, selectedPlatform]);

  // Helper functions
  function addDays(date, days) {
    const result = new Date(date);
    result.setDate(result.getDate() + days);
    return result;
  }

  function getDefaultDateRange() {
    const today = new Date();
    const threeMonthsAgo = new Date();
    threeMonthsAgo.setMonth(today.getMonth() - 3);
    return `${formatDate(threeMonthsAgo)} - ${formatDate(today)}`;
  }

  function formatDate(date) {
    return date.toLocaleDateString("en-US", {
      month: "short",
      day: "numeric",
      year: "numeric",
    });
  }

  const formatTime = useCallback((hour) => {
    if (hour === 0) return "12:00AM";
    if (hour < 12) return `${hour}:00AM`;
    if (hour === 12) return "12:00PM";
    return `${hour - 12}:00PM`;
  }, []);

  const getDaysInMonth = useCallback((date) => {
    const year = date.getFullYear();
    const month = date.getMonth();
    const firstDay = new Date(year, month, 1);
    const lastDay = new Date(year, month + 1, 0);
    const daysInMonth = lastDay.getDate();
    const startingDayOfWeek = firstDay.getDay();

    const days = [];

    // Add previous month's trailing days
    const prevMonthDays = new Date(year, month, 0).getDate();
    for (let i = startingDayOfWeek - 1; i >= 0; i--) {
      days.push({
        date: new Date(year, month - 1, prevMonthDays - i),
        isCurrentMonth: false,
      });
    }

    // Add current month days
    for (let day = 1; day <= daysInMonth; day++) {
      days.push({
        date: new Date(year, month, day),
        isCurrentMonth: true,
      });
    }

    // Add next month's leading days
    const totalCells = Math.ceil(days.length / 7) * 7;
    for (let day = 1; days.length < totalCells; day++) {
      days.push({
        date: new Date(year, month + 1, day),
        isCurrentMonth: false,
      });
    }

    return days;
  }, []);

  const getWeekDays = useCallback((date) => {
    const startOfWeek = new Date(date);
    const day = startOfWeek.getDay();
    const diff = startOfWeek.getDate() - day + (day === 0 ? -6 : 0);
    startOfWeek.setDate(diff);

    return Array.from({ length: 7 }, (_, i) => {
      const currentDay = new Date(startOfWeek);
      currentDay.setDate(startOfWeek.getDate() + i);
      return currentDay;
    });
  }, []);

  const getEventsForDate = useCallback(
    (date) => {
      return events.filter(
        (event) =>
          event.date.getDate() === date.getDate() &&
          event.date.getMonth() === date.getMonth() &&
          event.date.getFullYear() === date.getFullYear()
      );
    },
    [events]
  );

  const handlePrevious = useCallback(() => {
    setCurrentDate((prev) => {
      const newDate = new Date(prev);
      viewMode === "Week"
        ? newDate.setDate(newDate.getDate() - 7)
        : newDate.setMonth(newDate.getMonth() - 1);
      return newDate;
    });
  }, [viewMode]);

  const handleNext = useCallback(() => {
    setCurrentDate((prev) => {
      const newDate = new Date(prev);
      viewMode === "Week"
        ? newDate.setDate(newDate.getDate() + 7)
        : newDate.setMonth(newDate.getMonth() + 1);
      return newDate;
    });
  }, [viewMode]);

  const handleUploadPostOpen = () => setIsUploadDialogOpen(true);
  const handleUploadPostClose = () => setIsUploadDialogOpen(false);

  useEffect(() => {
    if (loading) {
      const timeout = setTimeout(() => {
        setLoading(false);
      }, 300);
      return () => clearTimeout(timeout);
    }
  }, [loading]);

  if (loading) {
    return <Loader />;
  }

  // Common header component
  const CalendarHeader = ({ title, dateRange }) => (
    <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between p-4 md:p-6 border-b gap-4">
      {/* Left Side */}
      <div className="flex flex-col justify-center items-center sm:flex-row sm:items-center sm:justify-between gap-3">
        <h1 className="text-xl sm:text-2xl font-semibold sm:min-w-[200px]">
          {title}
        </h1>
        <div className="flex items-center space-x-1">
          <button
            onClick={handlePrevious}
            className="p-2 hover:bg-gray-100 rounded"
          >
            <ChevronLeft className="w-4 h-4" />
          </button>
          <button
            onClick={() => setCurrentDate(new Date())}
            className="p-2 hover:bg-gray-100 rounded text-sm"
          >
            Today
          </button>
          <button
            onClick={handleNext}
            className="p-2 hover:bg-gray-100 rounded"
          >
            <ChevronRight className="w-4 h-4" />
          </button>
        </div>
      </div>

      {/* Right Side */}
      <div className="flex flex-wrap justify-evenly lg:justify-end items-center gap-2">
        {/* Platform Select + Create Post */}
        <div className="ps-1 flex flex-col md:flex-row gap-2 items-center rounded-[8px] p-[1px] w-full md:w-[300px] ">
          {/* Create Post Button */}
          <div
            className="flex items-center gap-1 text-black bg-white border border-[#E0E0E0] whitespace-nowrap w-full md:w-auto transition rounded-[8px] px-2 pe-5 py-2 cursor-pointer"
            onClick={handleUploadPostOpen}
          >
            <img src={plus_icon} alt="Post" className="w-3 h-3 " />
            <button className=" text-sm font-medium  whitespace-nowrap">
              Create Post
            </button>
          </div>
          {/* Platform Dropdown */}
          <div className="w-full" ref={dropdownRef}>
            <div className="relative">
              <div
                className={`flex items-center justify-between bg-white px-4 py-2 border border-[#E0E0E0] cursor-pointer hover:border-[#ccc] transition ${
                  isOpen ? "rounded-tl-[8px] rounded-tr-[8px]" : "rounded-[8px]"
                } ${isLoadingPlatforms ? "opacity-75 cursor-not-allowed" : ""}`}
                onClick={toggleDropdown}
              >
                <div className="flex items-center">
                  {isLoadingPlatforms ? (
                    <>
                      <span className="text-sm text-gray-500 whitespace-nowrap">
                        Loading platforms...
                      </span>
                    </>
                  ) : selectedPlatform ? (
                    <>
                      {selectedPlatform === "all" ? (
                        <>
                          <img
                            src={siteConstant?.SOCIAL_ICONS?.FLOWKAR_F}
                            alt="Flowkar Logo"
                            className="h-6 w-6 mr-3 bg-white rounded-full"
                          />
                          <span className="text-sm font-medium text-[#14181F] bg ">
                            All Platforms
                          </span>
                        </>
                      ) : (
                        <>
                          {getPlatformIcon(selectedPlatform) ? (
                            <img
                              src={getPlatformIcon(selectedPlatform)}
                              alt={selectedPlatform}
                              className="w-5 h-5 mr-2 rounded-full"
                            />
                          ) : (
                            <span className="text-lg mr-2"></span>
                          )}
                          <span className="text-sm font-medium capitalize text-[#14181F]">
                            {selectedPlatform}
                          </span>
                        </>
                      )}
                    </>
                  ) : (
                    <>
                      <span className="text-lg mr-2"></span>
                      <span className="text-sm text-gray-500 whitespace-nowrap">
                        Select Platform
                      </span>
                    </>
                  )}
                </div>
                {!isLoadingPlatforms && (
                  <>
                    {isOpen ? (
                      <ChevronUp className="w-4 h-4 text-gray-400" />
                    ) : (
                      <ChevronDown className="w-4 h-4 text-gray-400" />
                    )}
                  </>
                )}
              </div>

              {/* Dropdown Menu */}
              {isOpen && !isLoadingPlatforms && (
                <div className="absolute top-full left-0 right-0 bg-white border border-gray-200 rounded-bl-[8px] rounded-br-[8px] shadow-md z-20 max-h-60 overflow-y-auto transition-all">
                  {/* All Platforms Option */}
                  <div
                    key="all"
                    onClick={() => handlePlatformSelect("all")}
                    className={`flex items-center px-4 py-2 hover:bg-gray-100 hover:text-Red cursor-pointer transition-colors border-b border-gray-100 ${
                      selectedPlatform === "all"
                        ? "bg-[#362624c8] text-white"
                        : ""
                    }`}
                  >
                    <img
                      src={siteConstant?.SOCIAL_ICONS?.FLOWKAR_F}
                      alt="Flowkar Logo"
                      className="h-6 w-6 mr-3 bg-white rounded-full"
                    />
                    <span className="text-sm font-medium whitespace-nowrap">
                      All Platforms
                    </span>
                  </div>

                  {availablePlatforms.length > 0 ? (
                    availablePlatforms.map((platform) => (
                      <div
                        key={platform}
                        onClick={() => handlePlatformSelect(platform)}
                        className={`flex items-center px-4 py-2 hover:bg-gray-200 cursor-pointer transition-colors border-b border-gray-100 last:border-b-0 ${
                          selectedPlatform === platform
                            ? "bg-Red text-white"
                            : ""
                        }`}
                      >
                        {getPlatformIcon(platform) ? (
                          <img
                            src={getPlatformIcon(platform)}
                            alt={platform}
                            className="w-5 h-5 mr-3 rounded-full"
                          />
                        ) : (
                          <span className="text-lg mr-3"></span>
                        )}
                        <span className="text-sm font-medium capitalize">
                          {platform}
                        </span>
                      </div>
                    ))
                  ) : (
                    <div className="p-4 text-sm text-gray-500 text-center">
                      No platforms available
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>

        {/* View Mode Toggle */}
        <div className="flex w-full bg-[#563D391A] rounded-[8px] p-[1px] mt-2 sm:mt-0 md:w-[240px]">
          {["Month", "Week", "List"].map((mode, index) => (
            <button
              key={mode}
              onClick={() => setViewMode(mode)}
              className={`flex-1 py-2 text-sm text-center ${
                viewMode === mode
                  ? "bg-white border border-[#E0E0E0] rounded-[8px] text-[#14181F] font-medium"
                  : "text-sm font-medium text-[#A9ABAD]"
              } ${
                index === 0 ? "rounded-l-md" : index === 2 ? "rounded-r-md" : ""
              }`}
            >
              {mode}
            </button>
          ))}
        </div>
      </div>
    </div>
  );

  // Week View Component
  const WeekView = () => {
    const weekDays = getWeekDays(currentDate);
    const weekRange = `${formatDate(weekDays[0])} - ${formatDate(weekDays[6])}`;

    return (
      <div className="flex mb-16">
        {/* Time Column - 24 hours */}
        <div className="w-20 border-r border-gray-200 bg-[#F6F7F9]">
          <div className="h-16 border-b border-gray-200 flex items-center justify-center">
            <span className="text-xs text-gray-500 font-medium">GMT +5:30</span>
          </div>
          {Array.from({ length: 24 }, (_, i) => {
            const hour = i; // Now from 0 to 23
            return (
              <div
                key={hour}
                className="h-16 border-b border-gray-200 flex items-start justify-end pr-2 pt-1 "
              >
                <span className="text-sm text-[#14181F] font-normal">
                  {formatTime24Hour(hour)}
                </span>
              </div>
            );
          })}
        </div>

        {/* Days Grid */}
        <div className="flex-1 grid grid-cols-7">
          {weekDays.map((day, dayIndex) => {
            const dayEvents = getEventsForDate(day);
            const isToday = day.toDateString() === new Date().toDateString();

            return (
              <div
                key={dayIndex}
                className={`border-r border-gray-200 last:border-r-0 ${
                  isToday ? "" : ""
                }`}
              >
                {/* Day Header */}
                <div
                  className={`h-16 border-b border-gray-200 flex flex-col items-start pl-[10px] justify-center ${
                    isToday ? "bg-[#563D3966]" : ""
                  }`}
                >
                  <span
                    className={`text-lg font-bold mb-1 ${
                      isToday
                        ? "text-[#563D39] rounded-full w-6 h-6 flex items-center justify-center"
                        : "text-base text-[#181D25] font-bold"
                    }`}
                  >
                    {day.getDate()}
                  </span>
                  <span
                    className={`hidden md:block  font-normal text-sm ${
                      isToday ? "text-[#563D39]" : "text-[#14181F]"
                    }`}
                  >
                    {DAY_NAMES_LONG[dayIndex]}
                  </span>
                  <span
                    className={`md:hidden text-[#14181F] font-normal text-sm ${
                      isToday ? "text-[#563D39]" : "text-[#14181F]"
                    }`}
                  >
                    {DAY_NAMES_SHORT[dayIndex]}
                  </span>
                </div>

                {/* Time Slots */}
                <div className="relative">
                  {Array.from({ length: 24 }, (_, i) => (
                    <div
                      key={i}
                      className="h-16 border-b border-gray-200"
                    ></div>
                  ))}

                  {/* Events */}
                  {dayEvents.map((event, eventIndex) => {
                    const [hours, minutes] = event.time
                      ?.split(":")
                      .map(Number) || [13, 0];
                    const position = (hours * 60 + minutes) * (64 / 60);

                    return (
                      <div
                        key={eventIndex}
                        onClick={() => handlePostClick(event)}
                        className="absolute left-1 right-1 bg-[#563D39] text-white text-xs p-2 rounded-md cursor-pointer"
                        style={{ top: `${position + 4}px` }}
                      >
                        <div className="flex flex-col items-start justify-center gap-1">
                          <div className="truncate w-full">{event.title}</div>
                          <div>
                            <span className="text-white font-bold">
                              Scheduled at :{" "}
                            </span>
                            {event.time}
                          </div>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            );
          })}
        </div>
      </div>
    );
  };

  // Helper function to format time in 24-hour format
  function formatTime24Hour(hour) {
    return `${hour.toString().padStart(2, "0")}:00`;
  }

  // List View Component
  const ListView = () => {
    const [searchTerm, setSearchTerm] = useState("");
    const [tempThumbnails, setTempThumbnails] = useState({});

    // Generate thumbnails for posts that need them after render
    useEffect(() => {
      const postsNeedingThumbnails = (listViewData || []).filter((post) => {
        const files = post.files || post.media || [];
        return (
          files.length > 0 &&
          (!post.thumbnail_files || post.thumbnail_files.length === 0) &&
          !tempThumbnails[post.id]
        );
      });
      postsNeedingThumbnails.forEach((post) => {
        const files = post.files || post.media || [];
        const file = files[0];
        const sourceUrl =
          typeof file === "string" ? file : file.url || file.path || "";
        const { isVideo } = getFileType(file);
        if (isVideo) {
          // Video thumbnail generation
          const video = document.createElement("video");
          video.src = sourceUrl;
          video.crossOrigin = "anonymous";
          video.muted = true;
          video.playsInline = true;
          video.onloadeddata = () => {
            video.currentTime = 0;
          };
          video.onseeked = () => {
            const canvas = document.createElement("canvas");
            canvas.width = video.videoWidth;
            canvas.height = video.videoHeight;
            const ctx = canvas.getContext("2d");
            ctx.drawImage(video, 0, 0, canvas.width, canvas.height);
            const thumbnailUrl = canvas.toDataURL("image/jpeg");
            setTempThumbnails((prev) => ({ ...prev, [post.id]: thumbnailUrl }));
          };
          video.onerror = () => {
            setTempThumbnails((prev) => ({ ...prev, [post.id]: null }));
          };
        } else {
          // Image
          setTempThumbnails((prev) => ({ ...prev, [post.id]: sourceUrl }));
        }
      });
      // eslint-disable-next-line
    }, [listViewData]);

    const allListItems = listViewData?.map((post) => {
      const scheduledDate = new Date(post.scheduled_at || post.created_at);
      const platformNames =
        post.platform && typeof post.platform === "object"
          ? Object.keys(post.platform).filter(
              (key) => post.platform[key] === true
            )
          : [];
      const files = post.files || post.media || [];
      const hasVideo = files.some((file) => {
        const { isVideo } = getFileType(file);
        return isVideo;
      });
      return {
        id: post.id,
        content:
          post.title && post.title.trim() !== "" && post.title.trim() !== "''"
            ? post.title
            : post.description || "Untitled Post",
        date: scheduledDate.toLocaleDateString(),
        time: scheduledDate.toLocaleTimeString("en-US", {
          hour12: true,
          hour: "2-digit",
          minute: "2-digit",
        }),
        description: post.description,
        platform: post.platform,
        platformNames: platformNames,
        status: post.status,
        files: files,
        thumbnail_files: post.thumbnail_files || [],
        temp_thumbnail: tempThumbnails[post.id],
        is_video: hasVideo,
        user: post.user,
      };
    });

    // Filter items based on search term
    const filteredItems =
      allListItems?.filter((item) => {
        if (!searchTerm.trim()) return true;

        const searchLower = searchTerm.toLowerCase();

        // Search in ID, content, status, and platform names
        return (
          item.id.toString().toLowerCase().includes(searchLower) ||
          item.content.toLowerCase().includes(searchLower) ||
          item.status.toLowerCase().includes(searchLower) ||
          item.platformNames.some((platform) =>
            platform.toLowerCase().includes(searchLower)
          )
        );
      }) || [];

    const [currentPage, setCurrentPage] = useState(1);
    const itemsPerPage = 10;
    const totalPages = Math.ceil(filteredItems.length / itemsPerPage);
    const [isLoading, setIsLoading] = useState(false);

    // Delete modal state
    const [deleteModalOpen, setDeleteModalOpen] = useState(false);
    const [selectedPost, setSelectedPost] = useState(null);

    // Reset to first page when search term changes
    useEffect(() => {
      setCurrentPage(1);
    }, [searchTerm]);

    const handlePrevious = () => {
      if (currentPage > 1) setCurrentPage(currentPage - 1);
    };

    const handleNext = () => {
      if (currentPage < totalPages) setCurrentPage(currentPage + 1);
    };

    const handlePageClick = (page) => {
      setCurrentPage(page);
    };

    // Handle search input change
    const handleSearchChange = (e) => {
      setSearchTerm(e.target.value);
    };

    // Handle delete button click
    const handleDeleteClick = (e, post) => {
      e.stopPropagation(); // Prevent row click event
      setSelectedPost(post);
      setDeleteModalOpen(true);
    };

    // Handle delete modal close
    const handleDeleteModalClose = () => {
      setDeleteModalOpen(false);
      setSelectedPost(null);
      fetchListViewData();
    };

    const handleEditClick = (e, post) => {
      e.stopPropagation();
      const postToEdit = {
        id: post.id,
        title: post.content,
        description: post.description || "",
        tagged_in: post.tagged_in || "",
        hashtags: post.hashtags || [],
        files: post.files || [],
        status: post.status,
        platform: post.platform,
        user: post.user,
        scheduled_at: post.scheduled_at,
        created_at: post.created_at,
      };
      setEditPost(postToEdit);
      setIsEditDialogOpen(true);
      setIsEditPostPopupOpen(true);
    };

    const listItems = filteredItems.slice(
      (currentPage - 1) * itemsPerPage,
      currentPage * itemsPerPage
    );

    return (
      <div className="p-6">
        {/* Search Bar */}
        <div className="flex items-center mb-6">
          <div className="relative flex-1 max-w-md">
            <input
              type="text"
              placeholder="Search for id, Name Post..."
              className="pl-4 pr-10 py-2 border border-gray-300 rounded-md w-full"
              value={searchTerm}
              onChange={handleSearchChange}
            />
            <div className="absolute right-3 top-2.5">
              <svg
                className="w-5 h-5 text-gray-400"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                />
              </svg>
            </div>
          </div>
        </div>

        {/* No data message */}
        {!isListViewLoading && listViewData?.length === 0 && !searchTerm && (
          <div className="flex flex-col items-center justify-center text-center py-16 px-4">
            <div className="w-16 h-16 mb-4 text-gray-400">
              <svg
                className="w-full h-full"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                />
              </svg>
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-1">
              No Posts Available
            </h3>
            <p className="text-gray-500">
              There are no posts to display at the moment.
            </p>
          </div>
        )}

        {/* No search results message */}
        {searchTerm.trim() && filteredItems.length === 0 && (
          <div className="flex flex-col items-center justify-center text-center py-16 px-4">
            <div className="w-16 h-16 mb-4 text-gray-400">
              <svg
                className="w-full h-full"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                />
              </svg>
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-1">
              No Results Found
            </h3>
            <p className="text-gray-500">
              No posts found matching "{searchTerm}"
            </p>
          </div>
        )}

        {/* Loading state */}
        {isListViewLoading && (
          <div className="flex justify-center items-center py-16">
            <Spinner />
          </div>
        )}

        {/* Table - only show if there's data and not loading */}
        {!isListViewLoading &&
          listViewData?.length > 0 &&
          filteredItems.length > 0 && (
            <div className="overflow-hidden rounded-[16px] border border-[#E0E0E0]">
              {/* Desktop Table View - Hidden on mobile/tablet */}
              <div className="hidden md:block">
                <table className="w-full">
                  <thead className="bg-[#F6F6F6]">
                    <tr className="border-b">
                      <th className="text-left py-3 px-4"></th>
                      <th className="text-left py-3 px-4">Post Content</th>
                      <th className="text-left py-3 px-4">Date</th>
                      <th className="text-left py-3 px-4">Status</th>
                      <th className="text-left py-3 px-4">Platforms</th>
                      <th className="text-left py-3 px-4">Action</th>
                    </tr>
                  </thead>
                  <tbody>
                    {listItems.map((item, index) => (
                      <tr
                        key={item.id}
                        className="border-t border-[#E0E0E0] hover:bg-gray-50 cursor-pointer"
                        onClick={() => handlePostClick(item)}
                      >
                        <td className="py-4 px-4"></td>
                        <td className="py-4 px-4">
                          <div className="flex items-center">
                            <div className="w-10 h-10 bg-gray-200 rounded mr-3 overflow-hidden flex-shrink-0">
                              {item.files && item.files.length > 0 ? (
                                (() => {
                                  // First check if there are thumbnail files
                                  if (
                                    item.thumbnail_files &&
                                    item.thumbnail_files.length > 0
                                  ) {
                                    return (
                                      <img
                                        src={item.thumbnail_files[0]}
                                        alt="Media thumbnail"
                                        className="w-full h-full object-cover"
                                        onError={(e) => {
                                          console.error(
                                            "Thumbnail failed to load:",
                                            item.thumbnail_files[0]
                                          );
                                          e.target.style.display = "none";
                                        }}
                                      />
                                    );
                                  }

                                  // Then check for temporary thumbnail
                                  if (item.temp_thumbnail) {
                                    return (
                                      <img
                                        src={item.temp_thumbnail}
                                        alt="Temporary thumbnail"
                                        className="w-full h-full object-cover"
                                        onError={(e) => {
                                          console.error(
                                            "Temporary thumbnail failed to load:",
                                            item.temp_thumbnail
                                          );
                                          e.target.style.display = "none";
                                        }}
                                      />
                                    );
                                  }

                                  // If no thumbnails, check if it's a video
                                  if (item.is_video) {
                                    return (
                                      <img
                                        src="https://via.placeholder.com/40x40/374151/9CA3AF?text=📹"
                                        alt="Video placeholder"
                                        className="w-full h-full object-cover"
                                        onError={(e) => {
                                          // Fallback to video icon if placeholder image fails to load
                                          e.target.outerHTML = `<div class="w-full h-full flex items-center justify-center bg-gray-600 text-gray-300 text-xs">📹</div>`;
                                        }}
                                      />
                                    );
                                  }

                                  // If not a video and no thumbnails, show the actual file
                                  const mediaUrl =
                                    typeof item.files[0] === "string"
                                      ? item.files[0]
                                      : item.files[0].url ||
                                        item.files[0].path ||
                                        "";
                                  return (
                                    <img
                                      src={mediaUrl}
                                      alt="Post preview"
                                      className="w-full h-full object-cover"
                                      onError={(e) => {
                                        console.error(
                                          "Image failed to load:",
                                          mediaUrl
                                        );
                                        e.target.style.display = "none";
                                      }}
                                    />
                                  );
                                })()
                              ) : (
                                // Show placeholder image when no media files exist
                                <img
                                  src="https://via.placeholder.com/40x40/E5E7EB/9CA3AF?text=📷"
                                  alt="No media placeholder"
                                  className="w-full h-full object-cover"
                                  onError={(e) => {
                                    // Fallback to text if placeholder image fails to load
                                    e.target.outerHTML = `<div class="w-full h-full flex items-center justify-center text-gray-400 text-xs">📷</div>`;
                                  }}
                                />
                              )}
                            </div>
                            <span className="text-sm truncate max-w-[200px] block">
                              {item.content}
                            </span>
                          </div>
                        </td>
                        <td className="py-4 px-4">
                          <div className="text-sm">
                            <div>{item.date}</div>
                            <div className="text-gray-500">{item.time}</div>
                          </div>
                        </td>
                        <td className="py-4 px-4">
                          <span
                            className={`px-3 py-2 ${
                              item.status === "scheduled"
                                ? "bg-[#563D39] text-[#14181F]"
                                : "bg-[#166534] text-[#14181F]"
                            } text-white text-xs rounded-full font-medium`}
                          >
                            {item.status}
                          </span>
                        </td>
                        <td className="py-4 px-4">
                          <div className="flex justify-left items-center gap-1">
                            {item.platformNames &&
                            item.platformNames.length > 0 ? (
                              item.platformNames.map((platformName, idx) => {
                                const icon = getPlatformIcon(platformName);
                                return icon ? (
                                  <img
                                    key={idx}
                                    src={icon}
                                    alt={platformName}
                                    className="w-6 h-6 rounded-full"
                                    title={platformName}
                                  />
                                ) : null;
                              })
                            ) : (
                              <span className="text-gray-400 text-xs">
                                No platforms
                              </span>
                            )}
                          </div>
                        </td>
                        <td className="py-4 px-4">
                          <div className="flex space-x-2">
                            <button
                              className={`p-1 hover:bg-gray-100 rounded ${
                                item.status === "scheduled" ? "hidden" : ""
                              }`}
                              onClick={(e) => {
                                e.stopPropagation();
                                handleEditClick(e, item);
                                setIsEditPostPopupOpen(true);
                              }}
                            >
                              <img
                                src={editIcon}
                                alt="Edit"
                                className="w-5 h-5"
                              />
                            </button>
                            <button
                              className="p-1 hover:bg-gray-100 rounded"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleDeleteClick(e, item);
                              }}
                            >
                              <img
                                src={Delete}
                                alt="Delete"
                                className="w-5 h-5"
                              />
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>

              {/* Mobile/Tablet Card View - Visible on mobile/tablet only */}
              <div className="md:hidden">
                <div className="bg-[#F6F6F6] px-4 py-3 border-b border-[#E0E0E0]">
                  <h3 className="text-sm font-medium text-gray-700">Posts</h3>
                </div>
                <div className="divide-y divide-[#E0E0E0]">
                  {listItems.map((item, index) => (
                    <div
                      key={item.id}
                      className="p-4 hover:bg-gray-50 cursor-pointer"
                      onClick={() => handlePostClick(item)}
                    >
                      {/* Main Content Row */}
                      <div className="flex justify-center items-center space-x-4 mb-3">
                        <div className="w-12 h-12 sm:w-14 sm:h-14 bg-gray-200 rounded overflow-hidden flex-shrink-0">
                          {item.files && item.files.length > 0 ? (
                            (() => {
                              // First check if there are thumbnail files
                              if (
                                item.thumbnail_files &&
                                item.thumbnail_files.length > 0
                              ) {
                                return (
                                  <img
                                    src={item.thumbnail_files[0]}
                                    alt="Media thumbnail"
                                    className="w-full h-full object-cover"
                                    onError={(e) => {
                                      console.error(
                                        "Thumbnail failed to load:",
                                        item.thumbnail_files[0]
                                      );
                                      e.target.style.display = "none";
                                    }}
                                  />
                                );
                              }

                              // Then check for temporary thumbnail
                              if (item.temp_thumbnail) {
                                return (
                                  <img
                                    src={item.temp_thumbnail}
                                    alt="Temporary thumbnail"
                                    className="w-full h-full object-cover"
                                    onError={(e) => {
                                      console.error(
                                        "Temporary thumbnail failed to load:",
                                        item.temp_thumbnail
                                      );
                                      e.target.style.display = "none";
                                    }}
                                  />
                                );
                              }

                              // If no thumbnails, check if it's a video
                              if (item.is_video) {
                                return (
                                  <img
                                    src="https://via.placeholder.com/56x56/374151/9CA3AF?text=📹"
                                    alt="Video placeholder"
                                    className="w-full h-full object-cover"
                                    onError={(e) => {
                                      // Fallback to video icon if placeholder image fails to load
                                      e.target.outerHTML = `<div class="w-full h-full flex items-center justify-center bg-gray-600 text-gray-300 text-sm">📹</div>`;
                                    }}
                                  />
                                );
                              }

                              // If not a video and no thumbnails, show the actual file
                              const mediaUrl =
                                typeof item.files[0] === "string"
                                  ? item.files[0]
                                  : item.files[0].url ||
                                    item.files[0].path ||
                                    "";
                              return (
                                <img
                                  src={mediaUrl}
                                  alt="Post preview"
                                  className="w-full h-full object-cover"
                                  onError={(e) => {
                                    console.error(
                                      "Image failed to load:",
                                      mediaUrl
                                    );
                                    e.target.style.display = "none";
                                  }}
                                />
                              );
                            })()
                          ) : (
                            // Show placeholder image when no media files exist
                            <img
                              src="https://via.placeholder.com/56x56/E5E7EB/9CA3AF?text=📷"
                              alt="No media placeholder"
                              className="w-full h-full object-cover"
                              onError={(e) => {
                                // Fallback to text if placeholder image fails to load
                                e.target.outerHTML = `<div class="w-full h-full flex items-center justify-center text-gray-400 text-sm">📷</div>`;
                              }}
                            />
                          )}
                        </div>
                        <div className="flex-1 min-w-0">
                          <p className="text-sm sm:text-base text-gray-900 line-clamp-2 mb-2">
                            {item.content}
                          </p>
                          <div className="flex flex-wrap items-center gap-2 text-xs sm:text-sm text-gray-500">
                            <span>{item.date}</span>
                            <span className="hidden sm:inline">•</span>
                            <span>{item.time}</span>
                          </div>
                        </div>
                      </div>

                      {/* Status and Platforms Row */}
                      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3">
                        <div className="flex items-center gap-3">
                          <span
                            className={`px-2 py-1 sm:px-3 sm:py-2 ${
                              item.status === "scheduled"
                                ? "bg-[#563D39] text-[#14181F]"
                                : "bg-[#166534] text-[#14181F]"
                            } text-white text-xs rounded-full font-medium`}
                          >
                            {item.status}
                          </span>
                          <div className="flex items-center gap-1">
                            {item.platformNames &&
                            item.platformNames.length > 0 ? (
                              item.platformNames.map((platformName, idx) => {
                                const icon = getPlatformIcon(platformName);
                                return icon ? (
                                  <img
                                    key={idx}
                                    src={icon}
                                    alt={platformName}
                                    className="w-5 h-5 sm:w-6 sm:h-6 rounded-full"
                                    title={platformName}
                                  />
                                ) : null;
                              })
                            ) : (
                              <span className="text-gray-400 text-xs">
                                No platforms
                              </span>
                            )}
                          </div>
                        </div>

                        {/* Action Buttons */}
                        <div className="flex items-center gap-2">
                          <button
                            className={`p-2 hover:bg-gray-100 rounded-lg ${
                              item.status === "scheduled" ? "hidden" : ""
                            }`}
                            onClick={(e) => {
                              e.stopPropagation();
                              handleEditClick(e, item);
                              setIsEditPostPopupOpen(true);
                            }}
                          >
                            <img
                              src={editIcon}
                              alt="Edit"
                              className="w-5 h-5"
                            />
                          </button>
                          <button
                            className="p-2 hover:bg-gray-100 rounded-lg"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleDeleteClick(e, item);
                            }}
                          >
                            <img
                              src={Delete}
                              alt="Delete"
                              className="w-5 h-5"
                            />
                          </button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}

        {/* Pagination - only show if there are filtered results */}
        {filteredItems.length > 0 && (
          <Pagination
            totalPages={totalPages}
            currentPage={currentPage}
            handlePrevious={handlePrevious}
            handleNext={handleNext}
            handlePageClick={handlePageClick}
            isLoading={isLoading}
          />
        )}

        {/* Delete Modal */}
        <DeleteScheduledPostModel
          open={deleteModalOpen}
          handleDialogClose={handleDeleteModalClose}
          post={selectedPost}
        />
      </div>
    );
  };

  // Month View Component
  const MonthView = () => {
    const days = getDaysInMonth(currentDate);

    return (
      <div className="p-6">
        {/* Day Headers */}
        <div className="grid grid-cols-7 bg-[#F6F7F9] border-[1px] border-[#DCE0E5] border-b-0">
          {DAY_NAMES_SHORT.map((dayName, index) => (
            <div
              key={dayName}
              className={`p-3 text-center text-sm font-medium text-[#A9ABAD] ${
                index !== 0 ? "border-l border-[#DCE0E5] -ml-[1px]" : ""
              }`}
            >
              {dayName}
            </div>
          ))}
        </div>

        {/* Calendar Days */}
        <div className="grid grid-cols-7 border border-gray-200 rounded-md overflow-hidden">
          {days.map((dayObj, index) => {
            const dayEvents = dayObj.isCurrentMonth
              ? getEventsForDate(dayObj.date)
              : [];
            const isToday =
              dayObj.date.toDateString() === new Date().toDateString();

            return (
              <div
                key={index}
                className={`min-h-28 border-r border-b border-gray-200 p-2 ${
                  !dayObj.isCurrentMonth
                    ? "bg-gray-50 text-gray-400"
                    : isToday
                    ? "bg-[#563D3966] text-[#563D39] "
                    : "bg-white hover:bg-gray-50"
                }`}
              >
                <div
                  className={`mb-2 flex justify-between ${
                    dayObj.isCurrentMonth
                      ? "text-[#14181F] font-bold text-[14px] md:text-[18px]"
                      : "text-gray-400"
                  }`}
                >
                  <span>{dayObj.date.getDate()}</span>
                </div>

                {dayEvents.map((event, eventIndex) => (
                  <div
                    key={eventIndex}
                    onClick={() => handlePostClick(event)}
                    className="text-xs text-white p-1 rounded mb-1 bg-[#563D39] cursor-pointer"
                  >
                    <div className="flex flex-col items-start justify-center gap-1">
                      <div className="truncate w-full">{event.title}</div>
                      <div>
                        <span className="text-white font-bold">
                          Scheduled at :{" "}
                        </span>
                        {event.time}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            );
          })}
        </div>
      </div>
    );
  };

  const PostDetailsPopup = ({ post, onClose }) => {
    if (!post) return null;

    return (
      <Dialog
        open={!!post}
        onClose={onClose}
        maxWidth="md"
        fullWidth
        className="bg-transparent font-Ubuntu"
        PaperProps={{
          style: {
            maxHeight: "90vh",
            margin: "8px",
            borderRadius: "20px",
            boxShadow: "0 20px 60px rgba(0, 0, 0, 0.1)",
            border: "none",
          },
        }}
      >
        <div className="bg-white rounded-[20px] overflow-y-auto">
          {/* Clean Header */}
          <DialogTitle sx={{ padding: 0 }}>
            <Box
              sx={{
                padding: { xs: 2, sm: 3 },
                borderBottom: "1px solid #f1f5f9",
                backgroundColor: "#ffffff",
              }}
            >
              <Box
                display="flex"
                justifyContent="space-between"
                alignItems="center"
              >
                <Typography
                  variant="h6"
                  sx={{
                    fontWeight: "400",
                    color: "#000000",
                    fontSize: { xs: "1.125rem", sm: "1.25rem" },
                  }}
                >
                  Post Details
                </Typography>
                <IconButton
                  onClick={onClose}
                  sx={{
                    color: "#64748b",
                    backgroundColor: "#f8fafc",
                    width: 40,
                    height: 40,
                    "&:hover": {
                      backgroundColor: "#f1f5f9",
                      color: "#475569",
                    },
                    transition: "all 0.2s ease",
                  }}
                >
                  <CloseIcon fontSize="small" />
                </IconButton>
              </Box>
            </Box>
          </DialogTitle>

          {/* Content */}
          <DialogContent
            dividers={false}
            sx={{
              padding: { xs: 2, sm: 3, md: 4 },
              backgroundColor: "#ffffff",
              fontFamily: "Ubuntu",
            }}
          >
            {/* Post Preview - Keeping exact dimensions */}
            <Box
              mb={4}
              mt={4}
              display="flex"
              justifyContent="center"
              width="100%"
              px={{ xs: 1, sm: 2 }}
            >
              {post.files && post.files.length > 0 ? (
                <Grid
                  container
                  spacing={{ xs: 1, sm: 2 }}
                  justifyContent="center"
                  style={{
                    maxWidth: "100%",
                    width: "100%",
                  }}
                >
                  {post.files.map((file, index) => {
                    const fileUrl =
                      typeof file === "string"
                        ? file
                        : file.url || file.path || "";
                    const isImage = /\.(jpg|jpeg|png|gif|webp)$/i.test(fileUrl);
                    const isVideo = /\.(mp4|mov|avi|webm|MOV)$/i.test(fileUrl);

                    // Debug log for video files
                    if (isVideo) {
                      console.log("Video file detected:", {
                        url: fileUrl,
                        extension: fileUrl.split(".").pop(),
                        hasThumbnail:
                          post.thumbnail_files &&
                          post.thumbnail_files.length > 0,
                      });
                    }

                    return (
                      <Grid
                        item
                        xs={12}
                        sm={post.files.length > 1 ? 6 : 12}
                        key={index}
                        style={{
                          display: "flex",
                          justifyContent: "center",
                          alignItems: "center",
                        }}
                      >
                        {isImage ? (
                          <img
                            src={fileUrl}
                            alt={`Post media ${index}`}
                            className="md:w-[340px] w-[250px] md:h-[360px] h-[250px] inline-block rounded-[16px] object-cover mx-auto"
                            style={{
                              boxShadow: "0 8px 25px rgba(0, 0, 0, 0.08)",
                              border: "1px solid #f1f5f9",
                            }}
                            onError={(e) => {
                              console.error("Image failed to load:", fileUrl);
                              e.target.style.display = "none";
                            }}
                          />
                        ) : isVideo ? (
                          <div className="relative">
                            {/* Show thumbnail as fallback */}
                            {post.thumbnail_files &&
                              post.thumbnail_files.length > 0 && (
                                <img
                                  src={post.thumbnail_files[0]}
                                  alt="Video thumbnail"
                                  className="absolute inset-0 w-full h-full object-cover rounded-[16px]"
                                  style={{
                                    zIndex: 1,
                                    opacity: 0.8,
                                  }}
                                  onError={(e) => {
                                    console.error(
                                      "Thumbnail failed to load:",
                                      post.thumbnail_files[0]
                                    );
                                    e.target.style.display = "none";
                                  }}
                                />
                              )}
                            {(() => {
                              const extension = fileUrl
                                .split(".")
                                .pop()
                                .toLowerCase();
                              const isMovFile = extension === "mov";

                              return (
                                <video
                                  key={fileUrl} // Add key to force re-render when URL changes
                                  controls
                                  playsInline
                                  preload="metadata"
                                  className="md:w-[340px] w-[250px] md:h-[360px] h-[250px] inline-block rounded-[16px] object-cover mx-auto relative z-10"
                                  style={{
                                    boxShadow: "0 8px 25px rgba(0, 0, 0, 0.08)",
                                    border: "1px solid #f1f5f9",
                                    backgroundColor: "transparent",
                                  }}
                                  onError={(e) => {
                                    console.error("Video loading error:", {
                                      url: fileUrl,
                                      error: e.target.error,
                                      networkState: e.target.networkState,
                                      readyState: e.target.readyState,
                                      isMov: isMovFile,
                                    });

                                    // For MOV files, try multiple approaches
                                    if (isMovFile) {
                                      const videoElement = e.target;
                                      const currentSource =
                                        videoElement.querySelector("source");

                                      if (currentSource) {
                                        // Try different MIME types for MOV
                                        const mimeTypes = [
                                          "video/quicktime",
                                          "video/x-quicktime",
                                          "video/mp4",
                                          "video/mov",
                                        ];

                                        let currentIndex = mimeTypes.indexOf(
                                          currentSource.type
                                        );
                                        if (currentIndex === -1)
                                          currentIndex = 0;

                                        const nextIndex =
                                          (currentIndex + 1) % mimeTypes.length;
                                        currentSource.type =
                                          mimeTypes[nextIndex];

                                        console.log(
                                          `Retrying MOV with MIME type: ${mimeTypes[nextIndex]}`
                                        );
                                        videoElement.load();
                                      }
                                    } else {
                                      // For non-MOV files, hide on error
                                      e.target.style.display = "none";
                                    }
                                  }}
                                  onLoadedData={(e) => {
                                    console.log("Video loaded successfully:", {
                                      url: fileUrl,
                                      type: e.target.querySelector("source")
                                        ?.type,
                                      isMov: isMovFile,
                                    });
                                    // Hide thumbnail when video loads successfully
                                    const thumbnail =
                                      e.target.parentElement.querySelector(
                                        "img"
                                      );
                                    if (thumbnail) {
                                      thumbnail.style.display = "none";
                                    }
                                  }}
                                >
                                  <source
                                    src={fileUrl}
                                    type={(() => {
                                      if (isMovFile) {
                                        // Try video/quicktime first for MOV files
                                        return "video/quicktime";
                                      }

                                      // Handle other video formats
                                      const mimeTypes = {
                                        mp4: "video/mp4",
                                        webm: "video/webm",
                                        avi: "video/x-msvideo",
                                      };
                                      return (
                                        mimeTypes[extension] ||
                                        `video/${extension}`
                                      );
                                    })()}
                                  />
                                  <source src={fileUrl} type="video/mp4" />
                                  Your browser does not support the video tag.
                                </video>
                              );
                            })()}
                          </div>
                        ) : (
                          <Box
                            sx={{
                              backgroundColor: "#f8fafc",
                              borderRadius: "16px",
                              padding: 3,
                              textAlign: "center",
                              color: "#64748b",
                              width: "100%",
                              maxWidth: "300px",
                              border: "1px solid #e2e8f0",
                            }}
                          >
                            <Typography variant="body2" fontWeight="500">
                              Unsupported media type
                            </Typography>
                          </Box>
                        )}
                      </Grid>
                    );
                  })}
                </Grid>
              ) : (
                <Box
                  sx={{
                    backgroundColor: "#f8fafc",
                    borderRadius: "16px",
                    padding: { xs: 4, sm: 6, md: 8 },
                    textAlign: "center",
                    color: "#64748b",
                    width: "100%",
                    maxWidth: "400px",
                    border: "1px solid #e2e8f0",
                  }}
                >
                  <Typography variant="body1" fontWeight="500">
                    No media attached
                  </Typography>
                </Box>
              )}
            </Box>

            {/* Clean Post Details Section */}
            <Box
              sx={{
                backgroundColor: "#ffffff",
                borderRadius: "16px",
                border: "1px solid #f1f5f9",
                overflow: "hidden",
              }}
            >
              <Grid container>
                {/* Title */}
                <Grid item xs={12}>
                  <Box
                    sx={{
                      padding: { xs: 3, sm: 4 },
                      borderBottom: { xs: "1px solid #f1f5f9", md: "none" },
                      borderRight: { md: "1px solid #f1f5f9" },
                      height: "100%",
                    }}
                  >
                    <Typography
                      sx={{
                        wordBreak: "break-word",
                        color: "#563D39",
                        fontWeight: "500",
                        fontSize: "1rem",
                        display: "-webkit-box",
                        WebkitBoxOrient: "vertical",
                        overflow: "hidden",
                        WebkitLineClamp: showFullTitle ? "none" : 2,
                      }}
                    >
                      {post.content?.trim()
                        ? post.content
                        : "No title available"}
                    </Typography>

                    {post.content?.length > 100 && (
                      <Typography
                        sx={{
                          color: "#563D39",
                          fontSize: "0.85rem",
                          cursor: "pointer",
                          mt: 1,
                          display: "inline-block",
                          fontWeight: 600,
                        }}
                        onClick={() => setShowFullTitle(!showFullTitle)}
                      >
                        {showFullTitle ? "View Less" : "View More"}
                      </Typography>
                    )}
                  </Box>
                </Grid>

                {/* Description */}
                <Grid item xs={12}>
                  <Box
                    sx={{
                      padding: { xs: 3, sm: 4 },
                      borderTop: "1px solid #f1f5f9",
                    }}
                  >
                    <Typography
                      sx={{
                        wordBreak: "break-word",
                        color: "#563D39",
                        lineHeight: 1.6,
                        fontSize: "0.95rem",
                        display: "-webkit-box",
                        WebkitBoxOrient: "vertical",
                        overflow: "hidden",
                        WebkitLineClamp: showFullDescription ? "none" : 2,
                      }}
                    >
                      {post.description?.trim()
                        ? post.description
                        : "No description available"}
                    </Typography>

                    {post.description?.length > 150 && (
                      <Typography
                        sx={{
                          color: "#563D39",
                          fontSize: "0.85rem",
                          cursor: "pointer",
                          mt: 1,
                          display: "inline-block",
                          fontWeight: 600,
                        }}
                        onClick={() =>
                          setShowFullDescription(!showFullDescription)
                        }
                      >
                        {showFullDescription ? "View Less" : "View More"}
                      </Typography>
                    )}
                  </Box>
                </Grid>

                {/* Scheduled Time */}
                <Grid container>
                  {/* Scheduled Time */}
                  <Grid item xs={12} md={6}>
                    <Box
                      sx={{
                        padding: { xs: 3, sm: 4 },
                        borderTop: "1px solid #f1f5f9",
                        borderRight: { md: "1px solid #f1f5f9" },
                      }}
                    >
                      <Typography
                        variant="subtitle2"
                        sx={{
                          color: "#64748b",
                          fontWeight: "500",
                          fontSize: "0.875rem",
                          mb: 1,
                          letterSpacing: "0.5px",
                        }}
                      >
                        Scheduled Time
                      </Typography>
                      <Typography
                        sx={{
                          fontSize: "0.95rem",
                          color: "#563D39",
                          fontWeight: "500",
                        }}
                      >
                        {post?.time ? post.time : "Not scheduled"}
                      </Typography>
                    </Box>
                  </Grid>

                  {/* Status */}
                  <Grid item xs={12} md={6}>
                    <Box
                      sx={{
                        padding: { xs: 3, sm: 4 },
                        borderTop: "1px solid #f1f5f9",
                        borderRight: { md: "1px solid #f1f5f9" },
                        height: "100%",
                      }}
                    >
                      <Typography
                        variant="subtitle2"
                        sx={{
                          color: "#64748b",
                          fontWeight: "500",
                          fontSize: "0.875rem",
                          mb: 1,
                          letterSpacing: "0.5px",
                        }}
                      >
                        Status
                      </Typography>
                      <Chip
                        label={
                          post.is_scheduled === true ? "Scheduled" : post.status
                        }
                        sx={{
                          "& .MuiChip-label": {
                            color:
                              post.is_scheduled === true ||
                              post.status === "scheduled"
                                ? "#563D39"
                                : "#166534",
                            fontWeight: 500,
                            fontSize: "0.875rem",
                          },
                          borderRadius: "8px",
                          height: "32px",
                        }}
                        size="small"
                      />
                    </Box>
                  </Grid>

                  {/* Created By */}
                  <Grid item xs={12} md={6}>
                    <Box
                      sx={{
                        padding: { xs: 3, sm: 4 },
                        borderTop: "1px solid #f1f5f9",
                      }}
                    >
                      <Typography
                        variant="subtitle2"
                        sx={{
                          color: "#64748b",
                          fontWeight: "500",
                          fontSize: "0.875rem",
                          mb: 1,
                          letterSpacing: "0.5px",
                        }}
                      >
                        Created By
                      </Typography>
                      <Typography
                        sx={{
                          wordBreak: "break-word",
                          color: "#563D39",
                          fontWeight: "500",
                          fontSize: "0.95rem",
                        }}
                      >
                        {post.user?.name?.trim()
                          ? post.user.name
                          : "Unknown User"}
                      </Typography>
                    </Box>
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <Box
                      sx={{
                        padding: { xs: 3, sm: 4 },
                        borderTop: "1px solid #f1f5f9",
                      }}
                    >
                      <Typography
                        variant="subtitle2"
                        sx={{
                          color: "#64748b",
                          fontWeight: "500",
                          fontSize: "0.875rem",
                          mb: 1,
                          letterSpacing: "0.5px",
                        }}
                      >
                        Plateform
                      </Typography>
                      <Typography
                        sx={{
                          wordBreak: "break-word",
                          color: "#563D39",
                          fontWeight: "500",
                          fontSize: "0.95rem",
                        }}
                      >
                        {(() => {
                          const platformNames =
                            post.platform && typeof post.platform === "object"
                              ? Object.keys(post.platform).filter(
                                  (key) => post.platform[key] === true
                                )
                              : Array.isArray(post.platform)
                              ? post.platform
                              : [];
                          return (
                            <div className="flex justify-left items-center gap-1">
                              {platformNames.length > 0 ? (
                                platformNames.map((platformName, idx) => {
                                  const icon = getPlatformIcon(platformName);
                                  return icon ? (
                                    <img
                                      key={idx}
                                      src={icon}
                                      alt={platformName}
                                      className="w-6 h-6 rounded-full"
                                      title={platformName}
                                    />
                                  ) : null;
                                })
                              ) : (
                                <span className="text-gray-400 text-xs">
                                  No platforms
                                </span>
                              )}
                            </div>
                          );
                        })()}
                      </Typography>
                    </Box>
                  </Grid>
                </Grid>
              </Grid>
            </Box>
          </DialogContent>
        </div>
      </Dialog>
    );
  };

  // Main render
  return (
    <div className="p-6 bg-gray-50 min-h-screen font-Ubuntu mb-16">
      <div className="bg-white rounded-lg shadow-sm">
        {viewMode === "Week" && (
          <>
            <CalendarHeader
              title={`${
                MONTHS[currentDate.getMonth()]
              } ${currentDate.getFullYear()}`}
              dateRange={`${formatDate(
                getWeekDays(currentDate)[0]
              )} - ${formatDate(getWeekDays(currentDate)[6])}`}
            />
            <WeekView />
          </>
        )}

        {viewMode === "List" && (
          <>
            <CalendarHeader title="List" dateRange={null} />
            <ListView />
          </>
        )}

        {viewMode === "Month" && (
          <>
            <CalendarHeader
              title={`${
                MONTHS[currentDate.getMonth()]
              } ${currentDate.getFullYear()}`}
              dateRange={selectedDateRange}
            />
            <MonthView />
          </>
        )}
      </div>
      {selectedPost && (
        <PostDetailsPopup post={selectedPost} onClose={handleClosePopup} />
      )}

      {isEditPostPopupOpen && (
        // EditPostPopup
        <EditPostPopup
          post={editPost}
          onClose={handleEditClose}
          onSave={handlePostSave}
        />
      )}

      <UploadPost open={isUploadDialogOpen} onClose={handleUploadPostClose} />
    </div>
  );
};

export default CalendarComponent;

export { EditPostPopup };
