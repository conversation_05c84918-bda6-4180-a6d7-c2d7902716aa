import 'package:flowkar/core/utils/exports.dart';
import 'package:flowkar/features/authentication/bloc/auth_bloc.dart';
import 'package:flowkar/features/authentication/model/stored_user_account_model.dart';
import 'package:flowkar/core/services/multi_account_manager.dart';

class MultiAccountBottomSheet extends StatefulWidget {
  final Function(StoredUserAccount)? onAccountSelected;
  final VoidCallback? onAddAccountTapped;

  const MultiAccountBottomSheet({
    super.key,
    this.onAccountSelected,
    this.onAddAccountTapped,
  });

  @override
  State<MultiAccountBottomSheet> createState() => _MultiAccountBottomSheetState();
}

class _MultiAccountBottomSheetState extends State<MultiAccountBottomSheet> {
  StoredAccountsList? storedAccountsList;
  StoredUserAccount? currentUser;
  bool isLoading = true;
  late bool isLoginUser;

  @override
  void initState() {
    super.initState();
    _loadStoredAccounts();
    Logger.lOG("Login user Id:--- ${Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID) ?? ''}");
  }

  Future<void> _loadStoredAccounts() async {
    try {
      final accounts = await MultiAccountManager.getStoredAccounts();
      final current = await MultiAccountManager.getCurrentAccount();

      // If no current account from stored accounts, get from preferences
      StoredUserAccount? currentUserInfo = current ?? _getCurrentUserFromPreferences();

      setState(() {
        storedAccountsList = accounts;
        currentUser = currentUserInfo;
        isLoading = false;
      });
    } catch (e) {
      Logger.lOG("Error loading stored accounts: $e");
      // Even if there's an error, try to get current user from preferences
      setState(() {
        currentUser = _getCurrentUserFromPreferences();
        isLoading = false;
      });
    }
  }

  /// Get current user information from preferences as fallback
  StoredUserAccount? _getCurrentUserFromPreferences() {
    try {
      final name = Prefobj.preferences?.get(Prefkeys.NAME) ?? '';
      final username = Prefobj.preferences?.get(Prefkeys.USERNAME) ?? '';
      final profileImage = Prefobj.preferences?.get(Prefkeys.PROFILE) ?? '';
      final brandId = Prefobj.preferences?.get(Prefkeys.BRANDID) ?? 0;
      final userId = int.tryParse(Prefobj.preferences?.get(Prefkeys.USER_ID)?.toString() ?? '0') ?? 0;
      final token = Prefobj.preferences?.get(Prefkeys.AUTHTOKEN) ?? '';

      // Only create user if we have essential information
      if (name.isNotEmpty || username.isNotEmpty) {
        return StoredUserAccount(
          brandId: brandId,
          name: name.isNotEmpty ? name : 'User',
          profileImage: profileImage,
          username: username.isNotEmpty ? username : 'user',
          userId: userId,
          token: token,
        );
      }
    } catch (e) {
      Logger.lOG("Error getting current user from preferences: $e");
    }
    return null;
  }

  /// Get display user ID - show loginUserId if it's the same as userId, otherwise show userId
  String getDisplayUserId(StoredUserAccount account) {
    try {
      final loginUserId = Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID) ?? '';
      final userId = account.userId;

      // If loginUserId exists and matches userId, show loginUserId
      if (loginUserId.toString() == userId.toString()) {
        return loginUserId.toString();
      }

      // Otherwise show userId
      return userId.toString();
    } catch (e) {
      Logger.lOG("Error getting display user ID: $e");
      return account.userId.toString();
    }
  }

  @override
  Widget build(BuildContext context) {
    isLoginUser = Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID) == Prefobj.preferences?.get(Prefkeys.USER_ID);
    return DraggableScrollableSheet(
      initialChildSize: 0.6,
      minChildSize: 0.4,
      maxChildSize: 0.9,
      expand: false,
      builder: (context, scrollController) {
        return Container(
          decoration: BoxDecoration(
            color: Theme.of(context).scaffoldBackgroundColor,
            borderRadius: BorderRadius.vertical(
              top: Radius.circular(20.r),
            ),
          ),
          child: Column(
            children: [
              _buildDragHandle(context),
              _buildHeader(context),
              Expanded(
                child: SingleChildScrollView(
                  controller: scrollController,
                  child: Column(
                    children: [
                      // buildSizedBoxH(16),
                      if (isLoading)
                        _buildLoadingState()
                      else if (currentUser == null)
                        _buildNoUserState(context)
                      else ...[
                        _buildCurrentUserSection(context),
                        buildSizedBoxH(16),
                        if (storedAccountsList != null && storedAccountsList!.accounts.length > 1)
                          _buildOtherAccountsSection(context)
                        else
                          _buildEmptyAccountsMessage(context),
                      ],
                    ],
                  ),
                ),
              ),
              if (isLoginUser) _buildAddAccountButton(context),
              buildSizedBoxH(MediaQuery.of(context).padding.bottom + 16),
            ],
          ),
        );
      },
    );
  }
  // Widget build(BuildContext context) {
  //   return Container(
  //     decoration: BoxDecoration(
  //       color: Theme.of(context).scaffoldBackgroundColor,
  //       borderRadius: BorderRadius.vertical(
  //         top: Radius.circular(20.r),
  //       ),
  //     ),
  //     constraints: BoxConstraints(
  //       maxHeight: MediaQuery.of(context).size.height * 0.8,
  //       minHeight: MediaQuery.of(context).size.height * 0.4,
  //     ),
  //     child: Column(
  //       mainAxisSize: MainAxisSize.min,
  //       children: [
  //         _buildDragHandle(context),
  //         _buildHeader(context),
  //         SingleChildScrollView(
  //           // controller: scrollController,
  //           child: Column(
  //             children: [
  //               buildSizedBoxH(16),
  //               if (isLoading)
  //                 _buildLoadingState()
  //               else if (currentUser == null)
  //                 _buildNoUserState(context)
  //               else ...[
  //                 // Always show current user at the top
  //                 _buildCurrentUserSection(context),
  //                 buildSizedBoxH(16),
  //                 // Show other accounts if any exist
  //                 if (storedAccountsList != null && storedAccountsList!.accounts.length > 1)
  //                   _buildOtherAccountsSection(
  //                     context,
  //                   )
  //                 else
  //                   _buildEmptyAccountsMessage(context),
  //               ],
  //               _buildAddAccountButton(context),
  //             ],
  //           ),
  //         ),
  //         buildSizedBoxH(MediaQuery.of(context).padding.bottom + 16),
  //       ],
  //     ),
  //   );
  // }

  Widget _buildDragHandle(BuildContext context) {
    return Container(
      width: 40.w,
      height: 4.h,
      margin: EdgeInsets.symmetric(vertical: 8.h),
      decoration: BoxDecoration(
        color: Theme.of(context).dividerColor.withValues(alpha: 0.5),
        borderRadius: BorderRadius.circular(2.r),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
      child: Row(
        children: [
          Text(
            'Switch Account',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontSize: 18.sp,
                  fontWeight: FontWeight.w700,
                ),
          ),
          const Spacer(),
          IconButton(
            onPressed: () => Navigator.of(context).pop(),
            icon: Icon(
              Icons.close,
              size: 24.sp,
              color: Theme.of(context).iconTheme.color,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingState() {
    return Expanded(
      child: Center(
        child: CircularProgressIndicator(
          strokeWidth: 2.w,
          color: Theme.of(context).primaryColor,
        ),
      ),
    );
  }

  Widget _buildNoUserState(BuildContext context) {
    return Expanded(
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.account_circle_outlined,
              size: 64.sp,
              color: Theme.of(context).iconTheme.color?.withValues(alpha: 0.5),
            ),
            buildSizedBoxH(16),
            Text(
              'No user information found',
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    fontSize: 18.sp,
                    fontWeight: FontWeight.w600,
                    color: Theme.of(context).textTheme.bodyLarge?.color?.withValues(alpha: 0.8),
                  ),
            ),
            buildSizedBoxH(8),
            Text(
              'Please log in to continue',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontSize: 14.sp,
                    color: Theme.of(context).textTheme.bodyMedium?.color?.withValues(alpha: 0.6),
                  ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCurrentUserSection(BuildContext context) {
    // Use the current account from stored accounts if available, otherwise fallback to currentUser
    final displayUser = storedAccountsList?.currentAccount ?? currentUser!;

    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Current Account',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w600,
                  color: Theme.of(context).textTheme.titleMedium?.color?.withValues(alpha: 0.7),
                ),
          ),
          buildSizedBoxH(12),
          Container(
            decoration: BoxDecoration(
              color: Theme.of(context).primaryColor.withOpacity(0.2),
              borderRadius: BorderRadius.circular(100.r),
              border: Border.all(
                color: Theme.of(context).primaryColor,
                width: 2.w,
              ),
            ),
            // decoration: BoxDecoration(
            //   color: Theme.of(context).primaryColor.withValues(alpha: 0.05),
            //   borderRadius: BorderRadius.circular(100.r),
            // border: Border.all(
            //   color: Theme.of(context).primaryColor,
            //   width: 2.w,
            // ),
            // ),
            child: ListTile(
              contentPadding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 0.h),
              leading: Stack(
                children: [
                  CircleAvatar(
                    radius: 24.r,
                    backgroundColor: Theme.of(context).customColors.white,
                    backgroundImage:
                        displayUser.profileImage.isNotEmpty ? NetworkImage(displayUser.profileImage) : null,
                    child: displayUser.profileImage.isEmpty
                        ? CustomImageView(imagePath: AssetConstants.pngUser, height: 24.h, width: 24.w)
                        : null,
                  ),
                  Positioned(
                    bottom: 0,
                    right: 0,
                    child: Container(
                      decoration: BoxDecoration(
                        color: Theme.of(context).scaffoldBackgroundColor,
                        shape: BoxShape.circle,
                        // border: Border.all(
                        //   color: Theme.of(context).scaffoldBackgroundColor,
                        //   width: 0.5.w,
                        // ),
                      ),
                      child: Icon(
                        Icons.check_circle_rounded,
                        color: Theme.of(context).primaryColor,
                        size: 17.sp,
                      ),
                    ),
                  ),
                ],
              ),
              title: Text(
                displayUser.name,
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontSize: 14.sp,
                      fontWeight: FontWeight.w700,
                    ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
              subtitle: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  buildSizedBoxH(4),
                  Text(
                    '@${displayUser.username}',
                    style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                          fontSize: 12.sp,
                          fontWeight: FontWeight.w500,
                          color: Theme.of(context).textTheme.bodyLarge?.color?.withValues(alpha: 0.8),
                        ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  // buildSizedBoxH(2),
                  // Text(
                  //   'Brand ID: ${displayUser.brandId} | User ID: ${_getDisplayUserId(displayUser)}',
                  //   style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  //         fontSize: 13.sp,
                  //         color: Theme.of(context).textTheme.bodySmall?.color?.withValues(alpha: 0.6),
                  //       ),
                  // ),
                ],
              ),
              trailing: Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID).toString() == displayUser.userId.toString()
                  ? storedAccountsList != null && storedAccountsList!.accounts.length > 1
                      ? Container(
                          padding: EdgeInsets.symmetric(horizontal: 10.0.w, vertical: 5.0.h),
                          margin: EdgeInsets.only(right: 14.w),
                          decoration: BoxDecoration(
                              color: Theme.of(context).customColors.white.withOpacity(0.5),
                              borderRadius: BorderRadius.circular(100.r),
                              border: Border.all(
                                color: Theme.of(context).primaryColor,
                                width: 1.w,
                              )),
                          child: Text("Main"),
                        )
                      : SizedBox.shrink()
                  : SizedBox.shrink(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOtherAccountsSection(BuildContext context) {
    // Get the current account from stored accounts (more reliable than currentUser)
    final currentAccountFromStored = storedAccountsList!.currentAccount;

    // Filter out the current account from the list
    final otherAccounts = storedAccountsList!.accounts.where((account) {
      // Use stored accounts current account for comparison if available
      if (currentAccountFromStored != null) {
        return !(account.brandId == currentAccountFromStored.brandId &&
            account.userId == currentAccountFromStored.userId);
      }
      // Fallback to currentUser comparison
      return !(account.brandId == currentUser?.brandId && account.userId == currentUser?.userId);
    }).toList();

    if (otherAccounts.isEmpty) {
      return SizedBox.shrink();
    }

    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Other Accounts',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w600,
                  color: Theme.of(context).textTheme.titleMedium?.color?.withValues(alpha: 0.7),
                ),
          ),
          buildSizedBoxH(12),
          ListView.separated(
            shrinkWrap: true,
            physics: NeverScrollableScrollPhysics(),
            itemCount: otherAccounts.length,
            separatorBuilder: (context, index) => buildSizedBoxH(8),
            itemBuilder: (context, index) {
              final account = otherAccounts[index];
              return _buildOtherAccountTile(context, account);
            },
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyAccountsMessage(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 20.h),
      child: Center(
        child: Column(
          children: [
            Icon(
              Icons.account_circle_outlined,
              size: 48.sp,
              color: Theme.of(context).iconTheme.color?.withValues(alpha: 0.5),
            ),
            buildSizedBoxH(12),
            Text(
              'No other accounts',
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w500,
                    color: Theme.of(context).textTheme.bodyLarge?.color?.withValues(alpha: 0.7),
                  ),
            ),
            buildSizedBoxH(4),
            Text(
              'Add another account to switch between them',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontSize: 14.sp,
                    color: Theme.of(context).textTheme.bodyMedium?.color?.withValues(alpha: 0.5),
                  ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildOtherAccountTile(BuildContext context, StoredUserAccount account) {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).primaryColor.withOpacity(0.2),
        borderRadius: BorderRadius.circular(100.r),
      ),
      // decoration: BoxDecoration(
      //   color: Theme.of(context).cardColor,
      //   borderRadius: BorderRadius.circular(100.r),
      //   border: Border.all(
      //     color: Theme.of(context).dividerColor.withValues(alpha: 0.2),
      //     width: 1.w,
      //   ),
      // ),
      child: ListTile(
        contentPadding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 0),
        leading: CircleAvatar(
          radius: 24.r,
          backgroundColor: Theme.of(context).primaryColor.withValues(alpha: 0.1),
          backgroundImage: account.profileImage.isNotEmpty ? NetworkImage(account.profileImage) : null,
          child: account.profileImage.isEmpty
              ? CustomImageView(imagePath: AssetConstants.pngUser, height: 24.h, width: 24.w)
              : null,
        ),
        title: Text(
          account.name,
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontSize: 14.sp,
                fontWeight: FontWeight.w600,
              ),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            // buildSizedBoxH(2),
            Text(
              '@${account.username}',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontSize: 12.sp,
                    color: Theme.of(context).textTheme.bodyMedium?.color?.withValues(alpha: 0.7),
                  ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
            // buildSizedBoxH(2),
            // Text(
            //   'Brand ID: ${account.brandId}',
            //   style: Theme.of(context).textTheme.bodySmall?.copyWith(
            //         fontSize: 12.sp,
            //         color: Theme.of(context).textTheme.bodySmall?.color?.withValues(alpha: 0.5),
            //       ),
            // ),
          ],
        ),
        trailing: Padding(
          padding: EdgeInsets.only(right: 16.w),
          child: Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID).toString() == account.userId.toString()
              ? Container(
                  padding: EdgeInsets.symmetric(horizontal: 10.0.w, vertical: 5.0.h),
                  // margin: EdgeInsets.only(right: 14.w),
                  decoration: BoxDecoration(
                      color: Theme.of(context).customColors.white.withOpacity(0.5),
                      borderRadius: BorderRadius.circular(100.r),
                      border: Border.all(
                        color: Theme.of(context).primaryColor,
                        width: 1.w,
                      )),
                  child: Text("Main"),
                )
              : InkWell(
                  onTap: () {
                    NavigatorService.goBack();
                    showDialog(
                      context: context,
                      builder: (ctx) {
                        return StatefulBuilder(builder: (context, setState) {
                          return BlocBuilder<AuthBloc, AuthState>(
                            builder: (context, authState) {
                              return CustomAlertDialog(
                                imagePath: Assets.images.svg.setting.svgDailogDeleteAccount.path,
                                title: "Remove Account",
                                subtitle:
                                    "This will remove the account from this User. You’ll have to sign in again to access it later.\nWould you like to continue?",
                                onConfirmButtonPressed: () {
                                  context.read<AuthBloc>().add(RemoveStoredAccountEvent(accountIndex: account.userId));
                                },
                                confirmButtonText: Lang.of(context).lbl_yes,
                                cancelButtonText: Lang.of(context).lbl_no,
                                isLoading: authState.logOutLoading,
                              );
                            },
                          );
                        });
                      },
                    );
                  },
                  child: Container(
                    padding: EdgeInsets.symmetric(horizontal: 8.0.w, vertical: 8.0.h),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(100.r),
                    ),
                    child: CustomImageView(
                      imagePath: Assets.images.svg.setting.svgDeleteAccount.path,
                      height: 22.h,
                      width: 22.w,
                      color: Theme.of(context).primaryColor,
                    ),
                  )),
        ),
        onTap: () {
          widget.onAccountSelected?.call(account);
          Navigator.of(context).pop();
        },
      ),
    );
  }

  Widget _buildAddAccountButton(BuildContext context) {
    return Container(
      width: double.infinity,
      margin: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
      child: ElevatedButton.icon(
        onPressed: () {
          Navigator.of(context).pop();
          widget.onAddAccountTapped?.call();
        },
        icon: Icon(
          Icons.add_circle_outline,
          size: 22.sp,
          color: Theme.of(context).customColors.white,
        ),
        label: Text(
          'Add Other Account',
          style: TextStyle(
            fontSize: 16.sp,
            fontWeight: FontWeight.w600,
            color: Theme.of(context).customColors.white,
          ),
        ),
        style: ElevatedButton.styleFrom(
          backgroundColor: Theme.of(context).primaryColor,
          foregroundColor: Theme.of(context).customColors.white,
          padding: EdgeInsets.symmetric(vertical: 14.h),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12.r),
          ),
          elevation: 0,
        ),
      ),
    );
  }
}
