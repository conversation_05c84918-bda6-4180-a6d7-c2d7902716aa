import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import UploadPost from './index';
import { IntlContext } from '../../../App';
import { BrandProvider } from '../../../helpers/context/BrandContext';

// Mock the required modules
jest.mock('../../../helpers/Axios/axiosINstance', () => ({
  get: jest.fn(),
  post: jest.fn(),
}));

jest.mock('../../../helpers/context/storage', () => ({
  fetchFromStorage: jest.fn(() => ({})),
}));

// Create a mock store
const mockStore = configureStore({
  reducer: {
    profile: (state = { selectedUser: null }) => state,
    schedule: (state = { value: 0 }) => state,
  },
});

// Mock context values
const mockIntlContext = {
  messages: {
    USER_WEB: {
      CREATE_NEW_POST: 'Create New Post',
      DRAG_PHOTOS_AND_VIDEOS_HERE: 'Drag photos and videos here',
    },
  },
};

const mockBrandContext = {
  selectedBrand: { id: 1, name: 'Test Brand' },
};

// Test component wrapper
const TestWrapper = ({ children }) => (
  <Provider store={mockStore}>
    <IntlContext.Provider value={mockIntlContext}>
      <BrandProvider value={mockBrandContext}>
        {children}
      </BrandProvider>
    </IntlContext.Provider>
  </Provider>
);

describe('UploadPost DatePicker Functionality', () => {
  const mockProps = {
    open: true,
    onClose: jest.fn(),
    renderHighlightedText: jest.fn(),
    text: '',
    renderTextFunction: jest.fn(),
    suggestionData: [],
  };

  beforeEach(() => {
    jest.clearAllMocks();
    // Mock localStorage
    Object.defineProperty(window, 'localStorage', {
      value: {
        getItem: jest.fn(() => null),
        setItem: jest.fn(),
        removeItem: jest.fn(),
      },
      writable: true,
    });
  });

  test('should set default time to 15 minutes from calendar click time', async () => {
    // Mock the current time to a specific value for testing
    const mockClickTime = new Date('2024-01-01T10:30:00');
    const expectedDefaultTime = new Date('2024-01-01T10:45:00'); // 15 minutes later
    
    jest.spyOn(Date, 'now').mockImplementation(() => mockClickTime.getTime());
    jest.spyOn(global, 'Date').mockImplementation((...args) => {
      if (args.length === 0) {
        return mockClickTime;
      }
      return new (jest.requireActual('Date'))(...args);
    });

    render(
      <TestWrapper>
        <UploadPost {...mockProps} />
      </TestWrapper>
    );

    // Find and click the calendar icon
    const calendarButton = screen.getByRole('button', { 
      name: /schedule/i 
    });
    
    fireEvent.click(calendarButton);

    // Wait for the DatePicker to appear
    await waitFor(() => {
      expect(screen.getByDisplayValue(/10:45/)).toBeInTheDocument();
    });

    // Restore the original Date constructor
    global.Date.mockRestore();
    Date.now.mockRestore();
  });

  test('should use calendar click time as base for minimum time calculation', async () => {
    const mockClickTime = new Date('2024-01-01T14:20:00');
    
    jest.spyOn(Date, 'now').mockImplementation(() => mockClickTime.getTime());
    jest.spyOn(global, 'Date').mockImplementation((...args) => {
      if (args.length === 0) {
        return mockClickTime;
      }
      return new (jest.requireActual('Date'))(...args);
    });

    render(
      <TestWrapper>
        <UploadPost {...mockProps} />
      </TestWrapper>
    );

    // Click the calendar icon to capture the click time
    const calendarButton = screen.getByRole('button', { 
      name: /schedule/i 
    });
    
    fireEvent.click(calendarButton);

    // Verify that the DatePicker uses the click time as base
    // This would be verified by checking that times before 14:35 (click time + 15 min) are disabled
    await waitFor(() => {
      const datePicker = screen.getByRole('application');
      expect(datePicker).toBeInTheDocument();
    });

    // Restore mocks
    global.Date.mockRestore();
    Date.now.mockRestore();
  });

  test('should reset calendar click time when dialog closes', async () => {
    render(
      <TestWrapper>
        <UploadPost {...mockProps} />
      </TestWrapper>
    );

    // Click calendar icon to set click time
    const calendarButton = screen.getByRole('button', { 
      name: /schedule/i 
    });
    
    fireEvent.click(calendarButton);

    // Close the dialog
    fireEvent.click(screen.getByRole('button', { name: /close/i }));

    // Verify that the component state is reset
    expect(mockProps.onClose).toHaveBeenCalled();
  });
});
