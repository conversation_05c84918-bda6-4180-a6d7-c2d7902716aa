import React, { useState, useEffect } from 'react';
import { useAccountSwitchApiRefresh, useAccountSwitchRefresh } from '../../../helpers/hooks/useAccountSwitchRefresh.js';
import { triggerGlobalApiRefresh, debugRefreshListeners } from '../../../helpers/utils/globalApiRefresh.js';
import { fetchFromStorage } from '../../../helpers/context/storage.jsx';
import siteConstant from '../../../helpers/constant/siteConstant.js';

/**
 * Test component to demonstrate the Account Switch Refresh system
 * This component shows how the system works and can be used for testing
 */
const AccountSwitchTest = () => {
  const [refreshCount, setRefreshCount] = useState(0);
  const [lastRefreshTime, setLastRefreshTime] = useState(null);
  const [currentUser, setCurrentUser] = useState(null);
  const [apiCallLogs, setApiCallLogs] = useState([]);
  const [listenerCount, setListenerCount] = useState(0);

  // Simulate API calls
  const mockApiCall1 = async () => {
    console.log('🔄 Mock API Call 1 executed');
    addLog('Mock API Call 1 executed');
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500));
    return { data: 'API 1 data', timestamp: Date.now() };
  };

  const mockApiCall2 = async () => {
    console.log('🔄 Mock API Call 2 executed');
    addLog('Mock API Call 2 executed');
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 300));
    return { data: 'API 2 data', timestamp: Date.now() };
  };

  const mockApiCall3 = async () => {
    console.log('🔄 Mock API Call 3 executed');
    addLog('Mock API Call 3 executed');
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 200));
    return { data: 'API 3 data', timestamp: Date.now() };
  };

  // Add log entry
  const addLog = (message) => {
    const timestamp = new Date().toLocaleTimeString();
    setApiCallLogs(prev => [...prev.slice(-9), `[${timestamp}] ${message}`]);
  };

  // Custom refresh callback
  const handleAccountSwitchRefresh = (eventData) => {
    console.log('🔄 Account switch detected in test component:', eventData);
    setRefreshCount(prev => prev + 1);
    setLastRefreshTime(new Date().toLocaleString());
    addLog(`Account switch detected: ${eventData.action}`);
    
    // Update current user info
    const userData = fetchFromStorage(siteConstant?.INDENTIFIERS?.USERDATA);
    setCurrentUser(userData);
  };

  // Use the account switch refresh hooks
  useAccountSwitchRefresh(handleAccountSwitchRefresh);
  
  useAccountSwitchApiRefresh([
    mockApiCall1,
    mockApiCall2,
    mockApiCall3
  ], []);

  // Update listener count and current user on mount
  useEffect(() => {
    setListenerCount(debugRefreshListeners());
    const userData = fetchFromStorage(siteConstant?.INDENTIFIERS?.USERDATA);
    setCurrentUser(userData);
  }, []);

  // Manual refresh trigger
  const handleManualRefresh = () => {
    addLog('Manual refresh triggered');
    triggerGlobalApiRefresh({
      reason: 'Manual test refresh',
      source: 'AccountSwitchTest component'
    });
  };

  // Clear logs
  const clearLogs = () => {
    setApiCallLogs([]);
  };

  return (
    <div className="p-6 max-w-4xl mx-auto bg-white rounded-lg shadow-lg">
      <h2 className="text-2xl font-bold mb-6 text-gray-800">
        Account Switch Refresh System Test
      </h2>
      
      {/* Current Status */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <div className="bg-blue-50 p-4 rounded-lg">
          <h3 className="font-semibold text-blue-800">Refresh Count</h3>
          <p className="text-2xl font-bold text-blue-600">{refreshCount}</p>
        </div>
        
        <div className="bg-green-50 p-4 rounded-lg">
          <h3 className="font-semibold text-green-800">Active Listeners</h3>
          <p className="text-2xl font-bold text-green-600">{listenerCount}</p>
        </div>
        
        <div className="bg-purple-50 p-4 rounded-lg">
          <h3 className="font-semibold text-purple-800">Last Refresh</h3>
          <p className="text-sm text-purple-600">
            {lastRefreshTime || 'Never'}
          </p>
        </div>
      </div>

      {/* Current User Info */}
      <div className="bg-gray-50 p-4 rounded-lg mb-6">
        <h3 className="font-semibold text-gray-800 mb-2">Current User</h3>
        {currentUser ? (
          <div className="text-sm text-gray-600">
            <p><strong>Name:</strong> {currentUser.name || 'N/A'}</p>
            <p><strong>Username:</strong> {currentUser.username || 'N/A'}</p>
            <p><strong>User ID:</strong> {currentUser.userId || currentUser.user_id || 'N/A'}</p>
            <p><strong>Token:</strong> {currentUser.token ? '***' + currentUser.token.slice(-10) : 'N/A'}</p>
          </div>
        ) : (
          <p className="text-gray-500">No user data found</p>
        )}
      </div>

      {/* Controls */}
      <div className="flex gap-4 mb-6">
        <button
          onClick={handleManualRefresh}
          className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors"
        >
          Trigger Manual Refresh
        </button>
        
        <button
          onClick={clearLogs}
          className="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition-colors"
        >
          Clear Logs
        </button>
        
        <button
          onClick={() => setListenerCount(debugRefreshListeners())}
          className="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg transition-colors"
        >
          Update Listener Count
        </button>
      </div>

      {/* API Call Logs */}
      <div className="bg-gray-50 p-4 rounded-lg">
        <h3 className="font-semibold text-gray-800 mb-2">API Call Logs</h3>
        <div className="bg-black text-green-400 p-3 rounded font-mono text-sm max-h-60 overflow-y-auto">
          {apiCallLogs.length > 0 ? (
            apiCallLogs.map((log, index) => (
              <div key={index}>{log}</div>
            ))
          ) : (
            <div className="text-gray-500">No logs yet. Switch accounts or trigger manual refresh to see logs.</div>
          )}
        </div>
      </div>

      {/* Instructions */}
      <div className="mt-6 p-4 bg-yellow-50 rounded-lg">
        <h3 className="font-semibold text-yellow-800 mb-2">How to Test</h3>
        <ol className="text-sm text-yellow-700 space-y-1">
          <li>1. Switch accounts using the dropdown in the navbar</li>
          <li>2. Watch the refresh count increase and logs appear</li>
          <li>3. Check that current user info updates</li>
          <li>4. Use "Trigger Manual Refresh" to test manual triggering</li>
          <li>5. Open browser console to see detailed logs</li>
        </ol>
      </div>
    </div>
  );
};

export default AccountSwitchTest;
