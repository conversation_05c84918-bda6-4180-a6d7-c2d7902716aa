import React from 'react';
import { usePermissions } from '../../helpers/hooks/usePermissions';

/**
 * PermissionGuard component for conditional rendering based on user permissions
 * 
 * @param {Object} props - Component props
 * @param {string|Array} props.permission - Single permission name or array of permission names
 * @param {string} props.mode - 'all' (default) or 'any' - for multiple permissions
 * @param {React.ReactNode} props.children - Content to render if permission check passes
 * @param {React.ReactNode} props.fallback - Content to render if permission check fails (optional)
 * @param {boolean} props.disabled - If true, renders children with disabled styling/props
 * @param {string} props.disabledClassName - CSS class to apply when disabled
 * @param {Object} props.disabledProps - Props to spread when disabled
 * 
 * @returns {React.ReactNode} Rendered content based on permissions
 */
const PermissionGuard = ({
  permission,
  mode = 'all',
  children,
  fallback = null,
  disabled = false,
  disabledClassName = '',
  disabledProps = {}
}) => {
  const { hasPermission, hasAllPermissions, hasAnyPermission, isLoading } = usePermissions();

  // Show loading state if permissions are still loading
  if (isLoading) {
    return fallback;
  }

  let hasRequiredPermission = false;

  if (Array.isArray(permission)) {
    // Multiple permissions
    if (mode === 'any') {
      hasRequiredPermission = hasAnyPermission(permission);
    } else {
      hasRequiredPermission = hasAllPermissions(permission);
    }
  } else {
    // Single permission
    hasRequiredPermission = hasPermission(permission);
  }

  // If user doesn't have permission, show fallback or nothing
  if (!hasRequiredPermission) {
    return fallback;
  }

  // If disabled prop is true, render with disabled styling/props
  if (disabled) {
    return React.cloneElement(children, {
      ...disabledProps,
      className: `${children.props.className || ''} ${disabledClassName}`.trim(),
      disabled: true
    });
  }

  // User has permission, render children
  return children;
};

export default PermissionGuard;
