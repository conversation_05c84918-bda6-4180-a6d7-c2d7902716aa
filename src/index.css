@import url("https://fonts.googleapis.com/css2?family=Sacramento:wght@100;300;400;500;700;900&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Metrophobic&family=Ubuntu:ital,wght@0,300;0,400;0,500;0,700;1,300;1,400;1,500;1,700&display=swap");
@import url("https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.6.0/css/all.min.css");
@import "react-datepicker/dist/react-datepicker.css";

@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen",
    "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue",
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  overflow: hidden;
}


/* index.css or App.css */
.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
  height: 8px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: transparent;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background-color: #452f2f; /* gray-300 */
  border-radius: 15%;
  border: 4px solid transparent;
  background-clip: padding-box;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background-color: #9ca3af; /* gray-400 */
}


img {
  user-select: none;
  pointer-events: none;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, "Courier New",
    monospace;
}

::-webkit-scrollbar {
  width: 4px;
}

::-webkit-scrollbar-track {
  background: rgba(249, 249, 249, 1)
  ;
}

::-webkit-scrollbar-thumb {
  background-color: #674941;
}

.slick-track {
  display: flex;
  justify-content: center;
  align-items: center;
}

.MuiPaper-root.MuiDialog-paper {
  background-color: transparent !important;
}

.MuiInput-underline:after {
  border-bottom: none !important;
}

.MuiInput-underline:hover {
  border-bottom: none !important;
}

.MuiSwitch-root .MuiSwitch-track {
  background-color: #d9d9d9 !important;
}

.MuiSwitch-root .MuiSwitch-switchBase.Mui-checked+.MuiSwitch-track {
  background-color: #674941 !important;
}

.MuiSvgIcon-root {
  height: 30px !important;
  width: 30px !important;
  box-sizing: content-box !important;
}

.MuiTypography-root {
  font-family: unset !important;
  font-weight: unset !important;
  font-size: unset !important;
}

.header {
  background-color: #1212bc;
  transition: backdrop-filter 0.3s ease, background-color 0.3s ease;
}

.header-blur {
  backdrop-filter: blur(10px);
  background-color: rgb(255, 255, 255);
}

.video-player {
  object-fit: cover;
}

.recharts-legend-item-text {
  color: gray !important;
  color: #bc9b86;
}

/* Custom styles for react-slick dots */
.slick-dots {
  bottom: 5px;
}

.slick-dots li button::before {
  font-size: 8px;
  display: flex;
  justify-content: center;
  align-items: center;
  color: rgb(255, 255, 255);
}

.Visibility {
  height: 23px !important;
  width: 23px !important;
}

.thumbnail-img video {
  width: 56px !important;
  border-radius: 18px;
  object-fit: cover;
}

.react-datepicker__header {
  background-color: #cbbdb267 !important;
}

.react-datepicker__time-container .react-datepicker__time .react-datepicker__time-box ul.react-datepicker__time-list li.react-datepicker__time-list-item--selected {
  background-color: #674941 !important;
  align-items: center !important;
}

.react-datepicker__time-container .react-datepicker__time .react-datepicker__time-box ul.react-datepicker__time-list li.react-datepicker__time-list-item {
  display: flex !important;
}

.react-datepicker__day--selected {
  background-color: #956b49 !important;
}

.react-datepicker__current-month {
  color: #956b49 !important;
}

.react-datepicker-time__header {
  color: #956b49 !important;
}

.react-datepicker__day--in-range {
  background-color: #956b49a3 !important;
}

.react-datepicker__day--in-range:hover {
  background-color: #956b49a3 !important;
}

/* CustomDatePicker.css */

.react-datepicker__day--in-selecting-range {
  background-color: #956b49a3 !important;
  /* Change to your desired hover color */
}

/* @keyframes slideInUp {
  0% {
    transform: translateY(100%);
    opacity: 0;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
} */
@media (min-width: 1280px) and (max-width: 1400px) {
  .profile>*:not(:last-child) {
    margin-right: -12px !important;
    font-size: smaller !important;
  }
}

@media (min-width: 300px) and (max-width: 470px) {
  .Toastify__toast-container {
    width: 96vw;
    padding: 0;
    left: 5px;
    margin: 0;
    top: 3px;
  }

  .login-container {
    margin-top: 40px !important;
    padding: 20px 1px 0px 5px;
  }

  .edit-profile {
    min-width: 320px !important;
  }

  .login-title {
    font-size: 20px !important;
  }

  .login-text {
    font-size: 14px !important;
    margin-top: 5px;
  }

  .login-suggestion {
    font-size: 16.5px !important;
  }

  .login-bottom {
    font-size: 13px !important;
  }

  /* Chat Module Responsive Styles */
  .chat-message-bubble {
    max-width: 85% !important;
    font-size: 14px !important;
  }

  .chat-input {
    font-size: 16px !important; /* Prevents zoom on iOS */
  }

  .chat-header {
    padding: 8px 12px !important;
  }

  .chat-avatar {
    width: 32px !important;
    height: 32px !important;
  }

  /* admin */
  .title-child {
    font-size: 13px;
  }

  .scheduled {
    padding: 10px;
  }

  .scheduled .scheduled-sub {
    gap: 4px;
  }

  .scheduled .scheduled-user {
    height: 35px;
    width: 35px;
    border-radius: 15px !important;
  }

  .scheduled .scheduled-username {
    font-size: 12px;
  }

  .scheduled .scheduled-name {
    font-size: 14px;
  }

  .scheduled .scheduled-status {
    font-size: 10px;
    padding: 4px;
  }

  .scheduled .right-side {
    padding: 0px;
    gap: 4px;
  }

  .scheduled .scheduled-delete {
    height: 30px;
    width: 30px;
  }

  .recent-btn {
    padding-top: 12px !important;
  }
}

.css-12sw0nx-MuiDialogContent-root {
  padding: 0px !important;
}

.channel-scrollbar::-webkit-scrollbar {
  height: 6px !important;
  width: 6px !important;
}

.channel-scrollbar::-webkit-scrollbar-thumb {
  background-color: #B9A08B !important;
  border-radius: 10px !important;
}

.channel-scrollbar::-webkit-scrollbar-track {
  background-color: #B9A08B !important;
}

/* For Firefox */
.channel-scrollbar {
  scroll-behavior: smooth;
  scrollbar-color: #B9A08B #EFEBE9;
  scrollbar-width: thin;
}

@media (min-width: 300px) and (max-width: 400px) {
  .nav-inp {
    width: 120px;
  }

  .user-dashboard {
    height: 350px !important;
    min-width: 270px !important;
  }

  .admin-dashboard {
    height: 300px !important;
  }

  .admin-label {
    font-size: 12px !important;
  }

  .admin-count {
    font-size: 16px;
  }

  .admin-title {
    font-size: 18px;
  }

  .admin-month {
    font-size: 11.5px;
  }

  .admin-lasets {
    font-size: 11.5px;
  }

  .recharts-default-tooltip {
    font-size: 10px !important;
  }

  .recharts-legend-wrapper ul {
    margin-bottom: 10px !important;
  }

  .recent-text h5 {
    font-size: 15px !important;
  }

  .recent-text p {
    font-size: 14px !important;
  }

  .recent-bottom {
    font-size: 14px;
    margin-left: 2px;
  }

  .channel-box {
    height: 140px;
    width: 100%;
  }
  .channel-box .action-btn {
    padding: 5px 8px;
    font-size: small;
  }

  .channel-innerbox {
    height: 30px !important;
    width: 100%;
    padding-top: 10px;
  }

  .channel-text {
    font-size: 16px;
  }

  .channel-text2 {
    font-size: 14px;
  }

  .social-text {
    font-size: 14px !important;
  }

  .social-name {
    font-size: 10px;
  }

  .upload-header {
    font-size: 18px;
  }

  .upload-save {
    font-size: 13px;
  }

  .singlepost-head {
    font-size: 13.5px;
  }

  .editprofile-dailog {
    width: 240px !important;
  }

  .no-scheduled {
    font-size: 15px;
    padding-bottom: 0px;
  }

  .scheduled-text {
    font-size: 12px !important;
  }

  .upload-post-date .react-datepicker {
    width: 100% !important;
  }

  .single-post-img img {
    height: 13px;
    width: 13px;
  }

  .single-post-img p {
    font-size: 12px;
  }
}

.react-datepicker {
  font-family: "Ubuntu" !important;
}

.react-datepicker__day--keyboard-selected {
  background-color: #956b49 !important;
  color: white !important;
  font-weight: 600 !important;
}

.react-datepicker__day:hover {
  background-color: #956b49 !important;
  color: white !important;
  font-weight: 600 !important;
}

.react-datepicker__day--selected {
  background-color: #956b49 !important;
  color: white !important;
  font-weight: 600 !important;
}

.react-datepicker__day:hover {
  background-color: #b9a08bc4 !important;
  color: white !important;
  font-weight: 600 !important;
}

.react-datepicker__day--selected:hover {
  background-color: #956b49 !important;
  /* Keep it the same as the selected color */
  color: white !important;
}

/* date-picker */
@media (min-width: 768px) and (max-width: 1024px) {
  .upload-post-date .react-datepicker__month-container {
    width: 200px !important;
  }
}

@media (min-width: 1024px) {
  .edit-datepicker .react-datepicker__month-container {
    width: 460px !important;
  }
}

@media (min-width: 768px) and (max-width: 1023px) {
  .edit-datepicker .react-datepicker__month-container {
    width: 350px !important;
  }
}

@media (min-width: 640px) {
  .edit-datepicker .react-datepicker__month-container {
    width: 350px;
  }
}

@media (max-width: 640px) {
  .edit-datepicker .react-datepicker__month-container {
    width: 100% !important;
  }
}

input[type="date"] {
  font-family: "Ubuntu", sans-serif !important;
}

.font-sans {
  font-family: unset !important;
}

.css-1pco1d0-MuiPaper-root-MuiPopover-paper-MuiMenu-paper {
  border-radius: 26px !important;
  transition: opacity 0.3s ease, transform 0.2s ease;
  box-shadow: 0 0 6px #00000035 !important;
  border: 0.5px solid #00000017 !important;
}

.css-6hp17o-MuiList-root-MuiMenu-list {
  padding-top: 0px !important;
  padding-bottom: 0px !important;
}

.slick-dots {
  position: absolute;
  bottom: -22px;
}

.slick-dots li {
  margin: 0 -4.1px;
  position: relative;
}

.slick-dots li button {
  width: 40px;
  height: 54px;
  background-color: transparent;
  border: none;
  padding: 0;
}

/* Style for inactive dots */
/* .slick-dots li button::before {
  content: "";
  display: block;
  width: 15px;
  height: 15px;
  background-color: transparent;
  border-radius: 50%;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
} */

/* Inner dot for inactive state */
.slick-dots li button::after {
  content: "";
  display: block;
  width: 7px;
  height: 7px;
  background-color: lightslategray;
  border-radius: 50%;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.slick-dots li.slick-active button::after {
  background-color: #674941;
  /* Active dot color */
}

/* Hover effect */
.slick-dots li button:hover::after {
  background-color: #777;
  /* Dot color on hover */
}

/* Small device-specific styling */
@media (max-width: 480px) {
  .slick-dots li button {
    width: 20px;
    height: 20px;
  }

  .gridpost .slick-dots li {
    margin: 0 -4.1px;
    position: relative;
  }
}

.gridpost .slick-dots li button {
  width: 40px;
  background-color: transparent;
  border: none;
  padding: 0;
}

.gridpost .slick-dots li button::before {
  content: "";
  display: block;
  width: 15px;
  height: 15px;
  background-color: transparent;
  border: none;
  border-radius: 50%;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.gridpost .slick-dots li button::after {
  content: "";
  display: block;
  width: 7px;
  height: 7px;
  background-color: black;
  border-radius: 50%;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.gridpost .slick-dots li.slick-active button::after {
  background-color: #674941;
}

.gridpost .slick-dots li button:hover::after {
  background-color: #777;
}

@media (max-width: 480px) {
  .slick-dots li button {
    width: 20px;
    height: 20px;
  }

  .gridpost .slick-dots li {
    margin: 0 1px;
  }
}

.css-3jswtl-MuiToolbar-root {
  min-height: 49px !important;
}

.description-inp {
  font-weight: 500 !important;
  color: #674941 !important;
}

@keyframes slideInFromTop {
  from {
    transform: translateY(-100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.modal-slide-in {
  animation: slideInFromTop 0.5s ease-out forwards;
}


/* Custom styles for the DatePicker */
.react-datepicker-wrapper {
  width: 100%;
}

.react-datepicker {
  font-family: 'Ubuntu', sans-serif;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.react-datepicker__header {
  background-color: #f7f7f7;
  border-bottom: 1px solid #e2e8f0;
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
  padding-top: 8px;
}

.react-datepicker__navigation {
  top: 10px;
}

.react-datepicker__day--selected,
.react-datepicker__day--keyboard-selected {
  background-color: #5c4039 !important;
  color: white !important;
  border-radius: 50%;
}

.react-datepicker__day:hover {
  background-color: #f2f0f0;
  border-radius: 50%;
}

.react-datepicker__year-dropdown,
.react-datepicker__month-dropdown {
  background-color: white;
  border: 1px solid #e2e8f0;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.react-datepicker__year-option:hover,
.react-datepicker__month-option:hover {
  background-color: #f2f0f0;
}

.react-datepicker__year-option--selected,
.react-datepicker__month-option--selected {
  background-color: #5c4039;
  color: white;
}

/* Ensure the datepicker appears above other elements */
.datepicker-container {
  position: relative;
}

.datepicker-popup {
  position: absolute;
  z-index: 1000;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  margin-top: 5px;
  left: 0;
  width: 100%;
  width: 500px;
}

/* Mobile responsive styles */
@media (max-width: 640px) {
  .datepicker-popup {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    max-width: 90%;
    z-index: 1100;
  }

  /* Chat Module Mobile Optimizations */
  .chat-container {
    height: calc(100vh - 80px) !important;
  }

  .chat-messages {
    padding: 8px !important;
  }

  .chat-message-text {
    font-size: 14px !important;
    line-height: 1.4 !important;
  }

  .chat-timestamp {
    font-size: 11px !important;
  }

  .typing-indicator {
    padding: 6px 12px !important;
  }

  /* Mobile Sidebar Full Width */
  .mobile-sidebar-fullscreen {
    margin: 0 !important;
    padding: 0 !important;
    width: 100vw !important;
    height: 100vh !important;
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
  }
}

/* Tablet responsive styles */
@media (min-width: 641px) and (max-width: 1024px) {
  .chat-container {
    height: calc(100vh - 100px) !important;
  }

  .chat-message-bubble {
    max-width: 75% !important;
  }

  .chat-sidebar {
    width: 320px !important;
  }
}

/* Large screen optimizations */
@media (min-width: 1025px) {
  .chat-container {
    height: calc(100vh - 120px) !important;
  }

  .chat-message-bubble {
    max-width: 60% !important;
  }
}

.custom-dash-border {
  border-width: 2px;
  border-style: dashed;
  border-color: #000;
  border-image: repeating-linear-gradient(to right, #000 0, #000 8px, transparent 8px, transparent 16px) 1;
}


.react-datepicker {
  @apply font-sans border-gray-200 rounded-lg;
}

.react-datepicker__header {
  @apply bg-white border-b-0;
}

.react-datepicker__time-container {
  @apply w-auto !important;
}




.react-datepicker__time-list-item {
  @apply h-auto p-2 hover:bg-gray-100;
}

.react-datepicker__time-list-item--selected {
  @apply bg-[#674941] text-white !important;
}

/* in your global CSS */
.truncate-2-lines {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.thin-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: #d1d5db #f3f4f6;
}

.thin-scrollbar::-webkit-scrollbar {
  height: 4px;
}

.thin-scrollbar::-webkit-scrollbar-track {
  background: #f3f4f6;
  border-radius: 4px;
}

.thin-scrollbar::-webkit-scrollbar-thumb {
  background: #d1d5db;
  border-radius: 4px;
}

.thin-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #9ca3af;
}

input:-webkit-autofill {
  -webkit-box-shadow: 0 0 0px 1000px white inset !important;
  box-shadow: 0 0 0px 1000px white inset !important;
  -webkit-text-fill-color: #000 !important;
  transition: background-color 5000s ease-in-out 0s;
}





input:focus,
textarea:focus,
select:focus {
  outline: none;
  box-shadow: none;
}


.hide-scrollbar::-webkit-scrollbar {
  display: none;
}
.hide-scrollbar {
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
}
