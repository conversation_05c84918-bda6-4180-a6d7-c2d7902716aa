import { useState, useEffect } from 'react';
import { 
  getRawPermissions, 
  getFormattedPermissions, 
  hasPermission, 
  hasPermissionByNumber,
  hasAllPermissions,
  hasAnyPermission,
  getUserPermissions,
  hasNoPermissions,
  PERMISSIONS,
  PERMISSION_NUMBERS
} from '../utils/permissionUtils';
import { accountSwitchEmitter } from '../context/MultiAccountContext';

/**
 * Custom hook for managing user permissions
 * Provides reactive access to user permissions and utility functions
 * 
 * @returns {Object} Object containing permissions data and utility functions
 */
export const usePermissions = () => {
  const [rawPermissions, setRawPermissions] = useState([]);
  const [formattedPermissions, setFormattedPermissions] = useState({});
  const [isLoading, setIsLoading] = useState(true);

  // Function to update permissions from localStorage
  const updatePermissions = () => {
    try {
      const raw = getRawPermissions();
      const formatted = getFormattedPermissions();
      
      setRawPermissions(raw);
      setFormattedPermissions(formatted);
      setIsLoading(false);
    } catch (error) {
      console.error('Error updating permissions:', error);
      setIsLoading(false);
    }
  };

  // Initial load and setup listener for account switches
  useEffect(() => {
    // Load permissions initially
    updatePermissions();

    // Listen for account switch events to update permissions
    const unsubscribe = accountSwitchEmitter.subscribe((data) => {
      if (data.action === 'ACCOUNT_SWITCHED') {
        // Small delay to ensure localStorage is updated
        setTimeout(updatePermissions, 100);
      }
    });

    // Listen for localStorage changes (in case permissions are updated elsewhere)
    const handleStorageChange = (event) => {
      if (event.key === 'userPermissions' || event.key === 'userPermissionsFormatted') {
        updatePermissions();
      }
    };

    window.addEventListener('storage', handleStorageChange);

    // Cleanup
    return () => {
      unsubscribe();
      window.removeEventListener('storage', handleStorageChange);
    };
  }, []);

  return {
    // Raw data
    rawPermissions,
    formattedPermissions,
    isLoading,
    
    // Utility functions
    hasPermission: (permissionName) => hasPermission(permissionName),
    hasPermissionByNumber: (permissionNumber) => hasPermissionByNumber(permissionNumber),
    hasAllPermissions: (permissionNames) => hasAllPermissions(permissionNames),
    hasAnyPermission: (permissionNames) => hasAnyPermission(permissionNames),
    getUserPermissions: () => getUserPermissions(),
    hasNoPermissions: () => hasNoPermissions(),
    
    // Constants for easy access
    PERMISSIONS,
    PERMISSION_NUMBERS,
    
    // Manual refresh function
    refreshPermissions: updatePermissions
  };
};

export default usePermissions;
