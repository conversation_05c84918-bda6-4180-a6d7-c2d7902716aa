const URL = {
  LOGIN: "/login/",
  LOGI<PERSON>_SWITCH: "/login-switch/",
  SIGNUP: "/register/",
  GET_INDUSTRY: "/get-industry/",
  USER_TYPE_SELECTION: "/industry/",
  USERDATA_HOME: "/user-data-home/",
  SURVEY: "/survey/",
  UPDATE_SUBSCRIPTION: "/update-subscription/",
  FORGOT_PASSWORD: "/forgot-password/",
  OTP_VERIFY: "/verify-otp/",
  RESET_PASSWORD: "/reset-password/",
  USER_ADMIN_PROFILE: "/user-admin-profile/",
  UPLOAD_POST: "/upload-post/",
  USER_POSTS: "user-posts/",
  SCHEDULED_POST: "/scheduled-posts/",
  USER_MANAGEMENT_API: "/api-user/",
  EDIT_PROFILE: "/edit-profile/",
  DELETE_POST: "/delete-post/",
  DELETE_USER: "/delete-profile/",
  POST_MANAGEMENT_GET: "/api-post/",
  ADMIN_POST_DELETE: "/delete-post-admin/",
  ADMIN_USER_DELETE: "/delete-user-admin/",
  ADMIN_DASHBOARD: "/admim-dashboard/",
  ANALYTICS_DATA: "/analytics-data/",
  SHARE_PROFILE: "/share-profile/",
  REMOVE_SCHEDULED_POSTS: "/remove-scheduled-posts/",
  INSTAGRAM: "/instagram-url/?plateform=1",
  THREAD: "thread-url/?plateform=1",
  FACEBOOK: "facebook-url/?plateform=1",
  TIKTOK: "tiktok-url/?plateform=1",
  REDDIT: "reddit-auth/?plateform=1",
  TUMBLR: "tumblr/?plateform=1",
  VIMEO: "vimeo-url/?plateform=1",
  MASTODON: "mastodon-url/",
  PINTEREST: "pinterest-url/?plateform=1",
  LINKEDIN: "linkedin-url/?plateform=1",
  YOUTUBE: "youtube-url/?plateform=1",
  X: "twitter-login/?plateform=1",
  TELEGRAM: "telegram-send-code/",
  TELEGRAM_SIGNIN: "telegram-sign-in/",
  TELEGRAM_CHAT_LIST: "telegram-dialogs/",
  TELEGRAM_USER_CHATS: "telegram-user-chats/",
  TELEGRAM_SEND_MESSAGE: "telegram-send-message/",
  DISCONNECT_TELEGRAM: "telegram-logout/",
  DISCONNECT_X: "disconnect-twitter/",
  DISCONNECT_INSTAGRAM: "disconnect-instagram/",
  DISCONNECT_FACEBOOK: "disconnect-facebook",
  DISCONNECT_THREAD: "disconnect-thread",
  DISCONNECT_VIMEO: "disconnect-vimeo",
  DISCONNECT_PINTEREST: "disconnect-pinterest",
  DISCONNECT_LINKEDIN: "disconnect-linkedin",
  DISCONNECT_TUMBLR: "disconnect-tumblr",
  DISCONNECT_REDDIT: "disconnect-reddit",
  DISCONNECT_TIKTOK: "disconnect-tiktok",
  DISCONNECT_YOUTUBE: "disconnect-youtube",
  DISCONNECT_MASTODON: "disconnect-mastodon",
  GET_THIRDPARTY: "get-thirdparty/",
  GET_BRANDS: "/get-brands-web/",
  EDIT_BRAND: "/edit-brand-web/",
  DELETE_BRAND: "/delete-brand/",
  REGISTER_BRAND: "/register-brand/",
  FEEDBACK: "/feedback/",
  GET_INVITED_USERS: "/get-invited-users/",
  GET_INVITEE_USERS: "/get-invitee-users/",
  USER_STATUS: "/user-status/",
  ALL_CHAT_LIST: "/all-chat-list/",
  CHAT_LIST: "/chat-list/",
  CHAT_MESSAGE_LIST: "/chat-message-list/",
  SINGLE_CHAT_DETAILS: "single-chat-details/",
  USER_ROLES: "/user-roles/",
  USER_MANAGEMENT: "/user-management/",
  CREATE_ROLE: "/user-roles/",
  DEMOGRAPHY_LIST: "/demography-list/",
  SEND_FACEBOOK_MESSAGE: "/send-facebook-message/",
  SEND_INSTAGRAM_MESSAGE: "/send-instagram-message/",
  SEND_TELEGRAM_MESSAGE: "/send-telegram-message/",
  LIVE_USER: "/live-users/",
  SEARCH_LOCATION: "/search-location/",
  SOCKET_URL: "https://api.flowkar.com/",
  BASE_IMAGE_URL: "https://api.flowkar.com/",
  NOTIFICATION: "/onesignal-id/",
  ANALYTICS_FACEBOOK_GARPH_1: "/facebook-analytics-g1/",
  ANALYTICS_FACEBOOK_GARPH_2: "/facebook-analytics-g2/",
  ANALYTICS_LINKEDIN_GARPH_1: "/linkedin-analytics-g1/",
  ANALYTICS_LINKEDIN_GARPH_2: "/linkedin-analytics-g2/",
  ANALYTICS_LINKEDIN_GARPH_3: "/linkedin-analytics-g3/",
  ANALYTICS_INSTAGRAM_GARPH_1: "/instagram-analytics-g1/",
  ANALYTICS_INSTAGRAM_GARPH_2: "/instagram-analytics-g2/",
  ANALYTICS_YOUTUBE_GARPH_1: "/youtube-analytics-g1/",
  ANALYTICS_YOUTUBE_GARPH_2: "/youtube-analytics-g2/",
  ANALYTICS_THREADS_GARPH_1: "/threads-analytics-g1/",
  ANALYTICS_THREADS_GARPH_2: "/threads-analytics-g2/",
  ANALYTICS_THREADS_GARPH_3: "/threads-analytics-g3/",
  ANALYTICS_THREADS_GARPH_4: "/threads-analytics-g4/",
  ANALYTICS_THREADS_GARPH_5: "/threads-analytics-g5/",
  ANALYTICS_PINTEREST_GARPH_1: "/pinterest-analytics-g1/",
  ANALYTICS_PINTEREST_GARPH_2: "/pinterest-analytics-g2/",
  GET_PENDING_INVITED_USERS: "/get-invited-users/",
  GET_PENDING_INVITEE_USERS: "/get-invitee-users/",
  ACCEPT_REQUEST: "/approve-invite/",
  REVOKE_REQUEST: "/revoke-invite/",
  DECLINE_REQUEST: "/decline-invite/",
  GET_ROLES: "/role-data/",
  GET_USER_REELS_POST: "/get-user-reel-post/",
  GET_USER_TAG_POST: "/get-tagged-post/",
  GET_USER_DRAFT_POSTS: "/get-draft-post/",
  DELETE_DRAFT_POST: "/draft-delete/",
  POST_DRAFT_POSTS: "/draft-post-upload/",
  CURRENT_BRAND: "/current-brand/",
  SELECT_BRAND: "/select-brand/",
  SWITCH_USER_ACCOUNT: "/switch-user-account",
  TEST_PERMISSIONS: "/test-permissions/",
  SHARE_SINGLE_POST_WEB: "/share-post-web-link/",
  TODAY_SCHEDULED_POST: "/today-scheduled-posts/",
  SCHEDULED_POST_WEB: "/scheduled-posts-web/",
  WEB_LIST: "/web-list/",
  SCHEDULED_POST_DELETE: "/web-delete-post",
};

export { URL };
