import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import {
  Box,
  TextField,
  Button,
  Typography,
  Container,
  Paper,
  Alert,
  CircularProgress,
} from "@mui/material";
import { useFormik } from "formik";
import * as Yup from "yup";
import apiInstance from "../../../../helpers/Axios/axiosINstance";
import { URL } from "../../../../helpers/constant/Url";
import { saveToStorage } from "../../../../helpers/context/storage";
import siteConstant from "../../../../helpers/constant/siteConstant";
import { useMultiAccount } from "../../../../helpers/context/MultiAccountContext";

const AddAccountSignIn = () => {
  const navigate = useNavigate();
  const { addStoredAccount } = useMultiAccount();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");

  const validationSchema = Yup.object({
    email: Yup.string()
      .email("Invalid email address")
      .required("Email is required"),
    password: Yup.string()
      .min(6, "Password must be at least 6 characters")
      .required("Password is required"),
  });

  const formik = useFormik({
    initialValues: {
      email: "",
      password: "",
    },
    validationSchema,
    onSubmit: async (values) => {
      setLoading(true);
      setError("");

      try {
        const form = new FormData();
        form.append("creds", values.email);
        form.append("password", values.password);

        const { status, data } = await apiInstance.post(URL.LOGIN_SWITCH, form);

        if (data?.status && data?.token) {
          // Create account object for storage
          // Use brand ID from API response, fallback to stored brand or default
          const brandId =
            data.brand_id ||
            data.brandId ||
            parseInt(localStorage.getItem("BrandId"), 10) ||
            1;

          console.log(
            "AddAccount: Setting account with brandId:",
            brandId,
            "from sources:",
            {
              apiResponseBrandId: data.brand_id,
              apiResponseBrandIdAlt: data.brandId,
              storedBrandId: localStorage.getItem("BrandId"),
            }
          );

          const accountData = {
            userId: data.user_id || data.id,
            name: data.name,
            username: data.username,
            profileImage: data.profile_image || "",
            token: data.token,
            email: values.email,
            brandId: brandId,
            isMainAccount: false, // This is an added account, not the main account
          };

          // Add to stored accounts
          addStoredAccount(accountData);

          // Show success message and redirect
          setError(
            "Account added successfully! You can now switch to this account from the dropdown."
          );
          setTimeout(() => {
            navigate("/dashboard");
          }, 2000);
        } else {
          setError(data?.message || "Login failed");
        }
      } catch (error) {
        console.error("Login error:", error);
        setError(
          error?.message || "Login failed. Please check your credentials."
        );
      } finally {
        setLoading(false);
      }
    },
  });

  return (
    <Container maxWidth="sm">
      <Box
        sx={{
          minHeight: "100vh",
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
        }}
      >
        <Paper
          elevation={3}
          sx={{
            p: 4,
            width: "100%",
            maxWidth: 400,
            borderRadius: 2,
          }}
        >
          <Box sx={{ textAlign: "center", mb: 3 }}>
            <Typography variant="h4" component="h1" gutterBottom>
              Add Account
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Sign in with another account to manage it alongside your current
              account
            </Typography>
          </Box>

          {error && (
            <Alert
              severity={error.includes("successfully") ? "success" : "error"}
              sx={{ mb: 2 }}
            >
              {error}
            </Alert>
          )}

          <form onSubmit={formik.handleSubmit}>
            <TextField
              fullWidth
              id="email"
              name="email"
              label="Email or Username"
              value={formik.values.email}
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              error={formik.touched.email && Boolean(formik.errors.email)}
              helperText={formik.touched.email && formik.errors.email}
              margin="normal"
              variant="outlined"
            />

            <TextField
              fullWidth
              id="password"
              name="password"
              label="Password"
              type="password"
              value={formik.values.password}
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              error={formik.touched.password && Boolean(formik.errors.password)}
              helperText={formik.touched.password && formik.errors.password}
              margin="normal"
              variant="outlined"
            />

            <Button
              type="submit"
              fullWidth
              variant="contained"
              disabled={loading}
              sx={{
                mt: 3,
                mb: 2,
                py: 1.5,
                backgroundColor: "#674941",
                "&:hover": {
                  backgroundColor: "#563D39",
                },
              }}
            >
              {loading ? (
                <CircularProgress size={24} color="inherit" />
              ) : (
                "Add Account"
              )}
            </Button>

            <Button
              fullWidth
              variant="text"
              onClick={() => navigate("/dashboard")}
              sx={{ mt: 1 }}
            >
              Cancel
            </Button>
          </form>
        </Paper>
      </Box>
    </Container>
  );
};

export default AddAccountSignIn;
